#!/bin/bash

# 数据库配置
HOST="rm-j6c5pi6g38w98vy81.mysql.rds.aliyuncs.com"
PORT="3306"
DB="chinese_ai"
USER="chinese_ai"
PASSWORD="kEn7J*w-f"

# 安全提示
echo "警告：此操作将清空数据库 $DB 的所有数据！"
read -p "确认继续吗？(y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 1
fi

# 改进的删除所有表逻辑（处理空数据库情况）
echo "正在清空数据库..."
mysql -h $HOST -P $PORT -u $USER -p"$PASSWORD" $DB <<EOF
SET FOREIGN_KEY_CHECKS=0;
SET GROUP_CONCAT_MAX_LEN=32768;

-- 生成安全的DROP语句（空数据库时执行SELECT 1）
SELECT IFNULL(
    CONCAT('DROP TABLE IF EXISTS ', GROUP_CONCAT(CONCAT('\`', table_name, '\`')), ';'),
    'SELECT 1;'
) INTO @drops
FROM information_schema.tables
WHERE table_schema = '$DB';

-- 执行生成的语句
PREPARE stmt FROM @drops;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET FOREIGN_KEY_CHECKS=1;
EOF

# 检查清空操作是否成功
if [ $? -ne 0 ]; then
    echo "错误：清空数据库失败"
    exit 1
fi
echo "数据库已清空"

# 导入SQL文件
echo "开始导入SQL文件..."
for sql_file in *.sql; do
    if [ -f "$sql_file" ]; then
        echo "导入: $sql_file"
        mysql -h $HOST -P $PORT -u $USER -p"$PASSWORD" $DB < "$sql_file"

        # 检查导入结果
        if [ $? -ne 0 ]; then
            echo "错误：导入 $sql_file 失败"
            exit 1
        fi
    fi
done

echo "所有操作已完成！"