<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI对话质量检查工具 - 随机展示对话样本，帮助评估对话质量和效果">
    <meta name="keywords" content="对话质量,质量检查,AI评估,对话评分">
    <title>对话质量检查 - AI对话系统</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://unpkg.com">

    <!-- External dependencies -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <!-- Common styles -->
    <link rel="stylesheet" href="../assets/css/common.css">

    <!-- Page specific styles -->
    <style>
        /* 评分按钮特殊样式 */
        .rating-excellent {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .rating-good {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }
        .rating-average {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .rating-poor {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        /* 对话内容优化 */
        .dialogue-content {
            line-height: 1.6;
            word-break: break-word;
        }

        /* 统计卡片动画 */
        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b" role="navigation" aria-label="主导航">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="../index" class="text-blue-600 hover:text-blue-800 font-medium">
                        ← 返回首页
                    </a>
                    <span class="text-gray-300">|</span>
                    <span class="text-gray-600">对话质量检查</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="generator" class="text-gray-600 hover:text-gray-800">内容生成</a>
                </div>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-4 py-8" role="main">
        <!-- 页面标题 -->
        <header class="text-center mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-2">对话质量检查</h1>
            <p class="text-gray-600 max-w-2xl mx-auto">随机展示对话样本，通过人工评估帮助改进AI对话质量</p>
        </header>

        <!-- 控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="topicSelect" class="block text-sm font-medium text-gray-700 mb-2">选择话题</label>
                    <select id="topicSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">请选择话题...</option>
                    </select>
                </div>
                <div>
                    <label for="difficultySelect" class="block text-sm font-medium text-gray-700 mb-2">选择难度</label>
                    <select id="difficultySelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">请选择难度...</option>
                        <option value="easy">简单 (Easy)</option>
                        <option value="medium">中等 (Medium)</option>
                        <option value="hard">困难 (Hard)</option>
                    </select>
                </div>
                <div>
                    <button id="loadDialogues" class="w-full bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="loadText">加载对话</span>
                        <span id="loadSpinner" class="loading hidden inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full ml-2"></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 对话展示区域 -->
        <div id="dialoguesContainer" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 对话卡片将在这里动态生成 -->
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="text-center py-16">
            <div class="text-gray-400 text-6xl mb-4">💬</div>
            <h3 class="text-xl font-medium text-gray-600 mb-2">暂无对话数据</h3>
            <p class="text-gray-500">请选择话题和难度后点击"加载对话"按钮</p>
        </div>

        <!-- 统计信息 -->
        <div id="statsContainer" class="hidden mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">质量评估统计</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600" id="goodCount">0</div>
                    <div class="text-sm text-gray-600">优秀</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600" id="averageCount">0</div>
                    <div class="text-sm text-gray-600">一般</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-red-600" id="poorCount">0</div>
                    <div class="text-sm text-gray-600">较差</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-gray-600" id="totalCount">0</div>
                    <div class="text-sm text-gray-600">总计</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class DialogueQualityChecker {
            constructor() {
                this.dialogues = [];
                this.ratings = {};
                this.basePath = this.getBasePath();
                this.init();
            }

            // 检测当前路径是否包含 /ai 前缀
            getBasePath() {
                const path = window.location.pathname;
                return path.startsWith('/ai') ? '/ai' : '';
            }

            init() {
                this.loadTopics();
                this.bindEvents();
            }

            async loadTopics() {
                try {
                    const response = await axios.get(this.basePath + '/topics');
                    const topics = response.data;
                    const topicSelect = document.getElementById('topicSelect');

                    topics.forEach(topic => {
                        const option = document.createElement('option');
                        option.value = topic.topic_id;
                        option.textContent = `${topic.title_zh} (${topic.category})`;
                        topicSelect.appendChild(option);
                    });
                } catch (error) {
                    console.error('加载话题失败:', error);
                    this.showError('加载话题失败，请刷新页面重试');
                }
            }

            bindEvents() {
                document.getElementById('loadDialogues').addEventListener('click', () => {
                    this.loadDialogues();
                });
            }

            async loadDialogues() {
                const topicId = document.getElementById('topicSelect').value;
                const difficulty = document.getElementById('difficultySelect').value;

                if (!topicId || !difficulty) {
                    this.showError('请选择话题和难度');
                    return;
                }

                this.setLoading(true);

                try {
                    const response = await axios.get(this.basePath + '/random', {
                        params: { topic_id: topicId, difficulty: difficulty }
                    });

                    this.dialogues = response.data;
                    this.ratings = {};
                    this.renderDialogues();
                    this.hideEmptyState();
                    this.updateStats();
                } catch (error) {
                    console.error('加载对话失败:', error);
                    this.showError('加载对话失败，请重试');
                } finally {
                    this.setLoading(false);
                }
            }

            renderDialogues() {
                const container = document.getElementById('dialoguesContainer');
                container.innerHTML = '';

                this.dialogues.forEach((dialogue, index) => {
                    const card = this.createDialogueCard(dialogue, index);
                    container.appendChild(card);
                });
            }

            createDialogueCard(dialogue, index) {
                const card = document.createElement('div');
                card.className = 'dialogue-card bg-white rounded-lg shadow-md p-6';

                const dialogueItems = dialogue.dialogues.map(item => `
                    <div class="mb-3 p-3 rounded-lg ${item.role === 'system' ? 'bg-blue-50 border-l-4 border-blue-400' : 'bg-green-50 border-l-4 border-green-400'}">
                        <div class="flex items-center mb-2">
                            <span class="text-xs font-semibold px-2 py-1 rounded ${item.role === 'system' ? 'bg-blue-200 text-blue-800' : 'bg-green-200 text-green-800'}">
                                ${item.role === 'system' ? '系统' : '用户'}
                            </span>
                            <span class="text-xs text-gray-500 ml-2">顺序: ${item.sort_order}</span>
                        </div>
                        <div class="text-gray-800 font-medium mb-1">${item.content_zh}</div>
                        <div class="text-gray-600 text-sm">${item.content_vi}</div>
                    </div>
                `).join('');

                card.innerHTML = `
                    <div class="flex justify-between items-start mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">对话 #${index + 1}</h3>
                        <div class="text-xs text-gray-500">
                            <span class="bg-gray-100 px-2 py-1 rounded">ID: ${dialogue.template_id}</span>
                            <span class="bg-gray-100 px-2 py-1 rounded ml-1">${dialogue.difficulty}</span>
                        </div>
                    </div>

                    <div class="mb-6 max-h-96 overflow-y-auto">
                        ${dialogueItems}
                    </div>

                    <div class="border-t pt-4">
                        <p class="text-sm text-gray-600 mb-3">请评估这组对话的质量：</p>
                        <div class="flex gap-2">
                            <button class="quality-btn flex-1 py-2 px-4 rounded-md text-sm font-medium border transition-colors
                                ${this.ratings[dialogue.template_id] === 'good' ? 'bg-green-500 text-white border-green-500' : 'bg-white text-green-600 border-green-300 hover:bg-green-50'}"
                                onclick="qualityChecker.rateDialogue(${dialogue.template_id}, 'good')">
                                👍 优秀
                            </button>
                            <button class="quality-btn flex-1 py-2 px-4 rounded-md text-sm font-medium border transition-colors
                                ${this.ratings[dialogue.template_id] === 'average' ? 'bg-yellow-500 text-white border-yellow-500' : 'bg-white text-yellow-600 border-yellow-300 hover:bg-yellow-50'}"
                                onclick="qualityChecker.rateDialogue(${dialogue.template_id}, 'average')">
                                👌 一般
                            </button>
                            <button class="quality-btn flex-1 py-2 px-4 rounded-md text-sm font-medium border transition-colors
                                ${this.ratings[dialogue.template_id] === 'poor' ? 'bg-red-500 text-white border-red-500' : 'bg-white text-red-600 border-red-300 hover:bg-red-50'}"
                                onclick="qualityChecker.rateDialogue(${dialogue.template_id}, 'poor')">
                                👎 较差
                            </button>
                        </div>
                    </div>
                `;

                return card;
            }

            rateDialogue(templateId, rating) {
                this.ratings[templateId] = rating;
                this.updateStats();
                this.renderDialogues(); // 重新渲染以更新按钮状态
            }

            updateStats() {
                const stats = {
                    good: 0,
                    average: 0,
                    poor: 0,
                    total: Object.keys(this.ratings).length
                };

                Object.values(this.ratings).forEach(rating => {
                    stats[rating]++;
                });

                document.getElementById('goodCount').textContent = stats.good;
                document.getElementById('averageCount').textContent = stats.average;
                document.getElementById('poorCount').textContent = stats.poor;
                document.getElementById('totalCount').textContent = stats.total;

                if (stats.total > 0) {
                    document.getElementById('statsContainer').classList.remove('hidden');
                }
            }

            setLoading(loading) {
                const button = document.getElementById('loadDialogues');
                const text = document.getElementById('loadText');
                const spinner = document.getElementById('loadSpinner');

                if (loading) {
                    button.disabled = true;
                    text.textContent = '加载中...';
                    spinner.classList.remove('hidden');
                } else {
                    button.disabled = false;
                    text.textContent = '加载对话';
                    spinner.classList.add('hidden');
                }
            }

            hideEmptyState() {
                document.getElementById('emptyState').style.display = 'none';
            }

            showError(message) {
                this.showNotification(message, 'error');
            }

            showNotification(message, type = 'info') {
                // 创建通知容器（如果不存在）
                let container = document.getElementById('notification-container');
                if (!container) {
                    container = document.createElement('div');
                    container.id = 'notification-container';
                    container.className = 'fixed top-4 right-4 z-50 space-y-2';
                    document.body.appendChild(container);
                }

                // 创建通知元素
                const notification = document.createElement('div');
                const notificationId = 'notification-' + Date.now();
                notification.id = notificationId;

                // 根据类型设置样式
                let bgColor, borderColor, iconColor, icon;
                switch (type) {
                    case 'success':
                        bgColor = 'bg-green-50';
                        borderColor = 'border-green-200';
                        iconColor = 'text-green-600';
                        icon = '✓';
                        break;
                    case 'error':
                        bgColor = 'bg-red-50';
                        borderColor = 'border-red-200';
                        iconColor = 'text-red-600';
                        icon = '✕';
                        break;
                    case 'warning':
                        bgColor = 'bg-yellow-50';
                        borderColor = 'border-yellow-200';
                        iconColor = 'text-yellow-600';
                        icon = '⚠';
                        break;
                    default:
                        bgColor = 'bg-blue-50';
                        borderColor = 'border-blue-200';
                        iconColor = 'text-blue-600';
                        icon = 'ℹ';
                }

                notification.className = `${bgColor} ${borderColor} border rounded-lg p-4 shadow-lg max-w-sm transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;

                notification.innerHTML = `
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <span class="${iconColor} text-lg font-bold">${icon}</span>
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-sm font-medium text-gray-900">${message}</p>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                <span class="sr-only">关闭</span>
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                `;

                container.appendChild(notification);

                // 触发动画
                setTimeout(() => {
                    notification.classList.remove('translate-x-full', 'opacity-0');
                    notification.classList.add('translate-x-0', 'opacity-100');
                }, 10);

                // 自动移除通知
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.classList.add('translate-x-full', 'opacity-0');
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.remove();
                            }
                        }, 300);
                    }
                }, 5000);
            }
        }

        // 初始化应用
        const qualityChecker = new DialogueQualityChecker();
    </script>
</body>
</html>
