<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI对话系统测试工具 - 专业的对话生成与质量评估平台，支持多种AI模型和智能对话测试">
    <meta name="keywords" content="AI对话,对话生成,质量评估,AI测试,人工智能">
    <meta name="author" content="AI对话系统">
    <title>AI对话系统 - 专业的对话生成与质量评估工具</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="dns-prefetch" href="https://unpkg.com">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom styles -->
    <style>
        /* Performance optimized animations */
        .card-hover {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* Accessibility improvements */
        .focus-visible:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Loading state */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .card-hover {
                transition: none;
            }
            .loading-skeleton {
                animation: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Hero Section -->
    <header class="gradient-bg text-white py-16" role="banner">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">AI对话系统</h1>
            <p class="text-lg md:text-xl opacity-90 max-w-2xl mx-auto">专业的对话生成与质量评估工具，助力AI对话质量提升</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-16" role="main">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <!-- 对话质量检查 -->
            <article class="bg-white rounded-lg shadow-lg p-8 card-hover focus-visible" tabindex="0">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4" aria-hidden="true">
                        <span class="text-3xl" role="img" aria-label="对话图标">💬</span>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">对话质量检查</h2>
                    <p class="text-gray-600">随机展示10组对话，帮助评估对话质量</p>
                </div>

                <ul class="space-y-4 mb-6" role="list">
                    <li class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2 flex-shrink-0" aria-hidden="true"></span>
                        随机获取对话样本
                    </li>
                    <li class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2 flex-shrink-0" aria-hidden="true"></span>
                        支持话题和难度筛选
                    </li>
                    <li class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2 flex-shrink-0" aria-hidden="true"></span>
                        质量评估与统计
                    </li>
                </ul>

                <button
                    onclick="navigateTo('quality')"
                    class="block w-full bg-blue-600 text-white text-center py-3 rounded-md hover:bg-blue-700 focus:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium"
                    aria-label="开始对话质量检查">
                    开始质量检查
                </button>
            </article>

            <!-- 内容生成测试 -->
            <article class="bg-white rounded-lg shadow-lg p-8 card-hover focus-visible" tabindex="0">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4" aria-hidden="true">
                        <span class="text-3xl" role="img" aria-label="机器人图标">🤖</span>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">内容生成测试</h2>
                    <p class="text-gray-600">测试AI模型生成对话的质量和效果</p>
                </div>

                <ul class="space-y-4 mb-6" role="list">
                    <li class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-purple-400 rounded-full mr-2 flex-shrink-0" aria-hidden="true"></span>
                        支持多种AI模型切换
                    </li>
                    <li class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-purple-400 rounded-full mr-2 flex-shrink-0" aria-hidden="true"></span>
                        场景跟读 & 自由对话
                    </li>
                    <li class="flex items-center text-sm text-gray-600">
                        <span class="w-2 h-2 bg-purple-400 rounded-full mr-2 flex-shrink-0" aria-hidden="true"></span>
                        实时生成与质量评估
                    </li>
                </ul>

                <button
                    onclick="navigateTo('generator')"
                    class="block w-full bg-purple-600 text-white text-center py-3 rounded-md hover:bg-purple-700 focus:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors font-medium"
                    aria-label="开始内容生成测试">
                    开始生成测试
                </button>
            </article>
        </div>

        <!-- 功能特色 -->
        <section class="mt-16 text-center" aria-labelledby="features-heading">
            <h3 id="features-heading" class="text-2xl font-bold text-gray-800 mb-8">系统特色</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4" aria-hidden="true">
                        <span class="text-2xl" role="img" aria-label="闪电图标">⚡</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">高效测试</h4>
                    <p class="text-sm text-gray-600">快速生成和评估对话内容，提升工作效率</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4" aria-hidden="true">
                        <span class="text-2xl" role="img" aria-label="目标图标">🎯</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">精准评估</h4>
                    <p class="text-sm text-gray-600">多维度质量评估体系，确保对话质量</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4" aria-hidden="true">
                        <span class="text-2xl" role="img" aria-label="工具图标">🔧</span>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">灵活配置</h4>
                    <p class="text-sm text-gray-600">支持多种模型和参数调整，满足不同需求</p>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8" role="contentinfo">
        <div class="container mx-auto px-4 text-center">
            <p class="text-gray-400">AI对话系统测试工具 - 专注于对话质量提升</p>
            <p class="text-gray-500 text-sm mt-2">© 2024 AI对话系统. 保留所有权利.</p>
        </div>
    </footer>

    <script>
        // 应用程序初始化
        (function() {
            'use strict';

            // 检测当前路径是否包含 /ai 前缀
            function getBasePath() {
                const path = window.location.pathname;
                return path.startsWith('/ai') ? '/ai' : '';
            }

            // 导航到指定页面
            function navigateTo(page) {
                try {
                    const basePath = getBasePath();
                    const targetUrl = basePath + '/' + page;

                    // 添加加载状态
                    const button = event.target;
                    const originalText = button.textContent;
                    button.textContent = '加载中...';
                    button.disabled = true;

                    // 延迟导航以显示加载状态
                    setTimeout(() => {
                        window.location.href = targetUrl;
                    }, 200);
                } catch (error) {
                    console.error('导航失败:', error);
                    // 恢复按钮状态
                    if (event.target) {
                        event.target.textContent = originalText;
                        event.target.disabled = false;
                    }
                }
            }

            // 将函数暴露到全局作用域
            window.navigateTo = navigateTo;

            // 页面加载完成后的初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 添加键盘导航支持
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && e.target.classList.contains('focus-visible')) {
                        e.target.click();
                    }
                });

                // 预加载关键页面
                const preloadPages = ['quality', 'generator'];
                preloadPages.forEach(page => {
                    const link = document.createElement('link');
                    link.rel = 'prefetch';
                    link.href = getBasePath() + '/' + page;
                    document.head.appendChild(link);
                });
            });

            // 错误处理
            window.addEventListener('error', function(e) {
                console.error('页面错误:', e.error);
            });

        })();
    </script>
</body>
</html>
