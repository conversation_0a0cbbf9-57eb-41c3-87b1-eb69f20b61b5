/* AI对话系统 - 通用样式文件 */

/* 性能优化的动画 */
.card-hover {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
               box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
.loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 加载骨架屏 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-skeleton 1.5s infinite;
}

@keyframes loading-skeleton {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 代码块样式 */
.code-block {
    background: #1e293b;
    color: #e2e8f0;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    border-radius: 8px;
    padding: 16px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
    font-size: 14px;
    line-height: 1.5;
}

/* 模型标签样式 */
.model-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.model-gpt4o { 
    background: #10b981; 
    color: white; 
}

.model-gpt4o-mini { 
    background: #3b82f6; 
    color: white; 
}

.model-doubao { 
    background: #8b5cf6; 
    color: white; 
}

.model-default { 
    background: #6b7280; 
    color: white; 
}

/* 质量评分按钮 */
.quality-btn {
    transition: all 0.2s ease;
}

.quality-btn:hover {
    transform: scale(1.05);
}

/* 对话卡片样式 */
.dialogue-card {
    transition: all 0.3s ease;
}

.dialogue-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 渐变背景 */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 可访问性改进 */
.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* 通知组件样式 */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    max-width: 24rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid #10b981;
}

.notification.error {
    border-left: 4px solid #ef4444;
}

.notification.warning {
    border-left: 4px solid #f59e0b;
}

.notification.info {
    border-left: 4px solid #3b82f6;
}

/* 标签页样式 */
.tab-button {
    padding: 0.75rem 1.5rem;
    border-bottom: 2px solid transparent;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tab-button:hover {
    color: #374151;
    border-bottom-color: #d1d5db;
}

.tab-button.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

.tab-content {
    display: none;
}

.tab-content:not(.hidden) {
    display: block;
}

/* 响应式改进 */
@media (max-width: 768px) {
    .card-hover:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .dialogue-card:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .notification {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .card-hover,
    .dialogue-card,
    .quality-btn,
    .loading,
    .fade-in,
    .loading-skeleton,
    .notification,
    .tab-button {
        transition: none;
        animation: none;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .code-block {
        background: #000000;
        color: #ffffff;
        border: 1px solid #ffffff;
    }
    
    .model-badge {
        border: 1px solid currentColor;
    }
}

/* 打印样式 */
@media print {
    .card-hover,
    .dialogue-card {
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
    
    .gradient-bg {
        background: #374151 !important;
        color: white !important;
    }
    
    .notification {
        display: none;
    }
}

/* 工具类 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
