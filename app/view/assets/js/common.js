/**
 * AI对话系统 - 通用JavaScript工具库
 */

(function(window) {
    'use strict';

    // 通用工具类
    const Utils = {
        /**
         * 检测当前路径是否包含 /ai 前缀
         */
        getBasePath() {
            const path = window.location.pathname;
            return path.startsWith('/ai') ? '/ai' : '';
        },

        /**
         * 防抖函数
         */
        debounce(func, wait, immediate) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            };
        },

        /**
         * 节流函数
         */
        throttle(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        /**
         * 格式化日期
         */
        formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            const hours = String(d.getHours()).padStart(2, '0');
            const minutes = String(d.getMinutes()).padStart(2, '0');
            const seconds = String(d.getSeconds()).padStart(2, '0');

            return format
                .replace('YYYY', year)
                .replace('MM', month)
                .replace('DD', day)
                .replace('HH', hours)
                .replace('mm', minutes)
                .replace('ss', seconds);
        },

        /**
         * 深拷贝对象
         */
        deepClone(obj) {
            if (obj === null || typeof obj !== 'object') return obj;
            if (obj instanceof Date) return new Date(obj.getTime());
            if (obj instanceof Array) return obj.map(item => this.deepClone(item));
            if (typeof obj === 'object') {
                const clonedObj = {};
                for (const key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        clonedObj[key] = this.deepClone(obj[key]);
                    }
                }
                return clonedObj;
            }
        },

        /**
         * 生成唯一ID
         */
        generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }
    };

    // 通知系统
    const Notification = {
        container: null,

        init() {
            if (!this.container) {
                this.container = document.createElement('div');
                this.container.id = 'notification-container';
                this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
                document.body.appendChild(this.container);
            }
        },

        show(message, type = 'info', duration = 5000) {
            this.init();

            const notification = document.createElement('div');
            const notificationId = 'notification-' + Utils.generateId();
            notification.id = notificationId;

            const colors = {
                success: 'bg-green-50 border-green-200 text-green-800',
                error: 'bg-red-50 border-red-200 text-red-800',
                warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
                info: 'bg-blue-50 border-blue-200 text-blue-800'
            };

            const icons = {
                success: '✓',
                error: '✕',
                warning: '⚠',
                info: 'ℹ'
            };

            notification.className = `notification ${colors[type] || colors.info} border rounded-lg p-4 shadow-lg max-w-sm transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;

            notification.innerHTML = `
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <span class="text-lg font-bold">${icons[type] || icons.info}</span>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <div class="ml-4 flex-shrink-0">
                        <button onclick="Notification.hide('${notificationId}')" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                            <span class="sr-only">关闭</span>
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            `;

            this.container.appendChild(notification);

            // 触发动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full', 'opacity-0');
                notification.classList.add('translate-x-0', 'opacity-100');
            }, 10);

            // 自动移除
            if (duration > 0) {
                setTimeout(() => {
                    this.hide(notificationId);
                }, duration);
            }

            return notificationId;
        },

        hide(notificationId) {
            const notification = document.getElementById(notificationId);
            if (notification) {
                notification.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }
        },

        success(message, duration) {
            return this.show(message, 'success', duration);
        },

        error(message, duration) {
            return this.show(message, 'error', duration);
        },

        warning(message, duration) {
            return this.show(message, 'warning', duration);
        },

        info(message, duration) {
            return this.show(message, 'info', duration);
        }
    };

    // 加载状态管理
    const Loading = {
        show(element, text = '加载中...') {
            if (typeof element === 'string') {
                element = document.getElementById(element);
            }
            if (!element) return;

            element.disabled = true;
            element.dataset.originalText = element.textContent;
            element.innerHTML = `
                <span class="inline-flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    ${text}
                </span>
            `;
        },

        hide(element) {
            if (typeof element === 'string') {
                element = document.getElementById(element);
            }
            if (!element) return;

            element.disabled = false;
            element.textContent = element.dataset.originalText || '确定';
            delete element.dataset.originalText;
        }
    };

    // HTTP请求工具
    const Http = {
        async request(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                ...options
            };

            try {
                const response = await fetch(url, defaultOptions);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                } else {
                    return await response.text();
                }
            } catch (error) {
                console.error('Request failed:', error);
                throw error;
            }
        },

        get(url, options = {}) {
            return this.request(url, { ...options, method: 'GET' });
        },

        post(url, data, options = {}) {
            return this.request(url, {
                ...options,
                method: 'POST',
                body: JSON.stringify(data)
            });
        },

        put(url, data, options = {}) {
            return this.request(url, {
                ...options,
                method: 'PUT',
                body: JSON.stringify(data)
            });
        },

        delete(url, options = {}) {
            return this.request(url, { ...options, method: 'DELETE' });
        }
    };

    // 暴露到全局
    window.Utils = Utils;
    window.Notification = Notification;
    window.Loading = Loading;
    window.Http = Http;

    // 页面通用初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化通知系统
        Notification.init();

        // 添加全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
            Notification.error('页面发生错误，请刷新重试');
        });

        // 添加全局未处理的Promise拒绝处理
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise拒绝:', e.reason);
            Notification.error('操作失败，请重试');
        });
    });

})(window);
