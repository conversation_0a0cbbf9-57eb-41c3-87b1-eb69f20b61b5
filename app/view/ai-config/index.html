<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI配置管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">AI配置管理</h1>
        
        <!-- 当前配置状态 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">当前配置状态</h2>
            <div id="current-config" class="text-gray-600">
                加载中...
            </div>
        </div>

        <!-- 配置列表 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-700">AI配置列表</h2>
                <button onclick="showCreateModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    新增配置
                </button>
            </div>
            <div id="config-list" class="space-y-4">
                加载中...
            </div>
        </div>

        <!-- 平台和模型信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- 平台列表 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">支持的AI平台</h2>
                <div id="platform-list" class="space-y-2">
                    加载中...
                </div>
            </div>

            <!-- 模型列表 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">可用模型</h2>
                <div id="model-list" class="space-y-2">
                    选择平台查看模型...
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑配置模态框 -->
    <div id="config-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h3 id="modal-title" class="text-lg font-semibold text-gray-800 mb-4">新增AI配置</h3>
            <form id="config-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">配置名称</label>
                    <input type="text" id="config-name" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">AI平台</label>
                    <select id="platform-select" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                        <option value="">选择平台</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">AI模型</label>
                    <select id="model-select" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                        <option value="">选择模型</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API密钥</label>
                    <input type="password" id="api-key" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API端点 (可选)</label>
                    <input type="url" id="api-endpoint" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">温度参数</label>
                    <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.8" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="is-default" class="mr-2">
                    <label for="is-default" class="text-sm text-gray-700">设为默认配置</label>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let platforms = [];
        let models = [];
        let configurations = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPlatforms();
            loadConfigurations();
            checkCurrentConfig();
        });

        // 加载平台列表
        async function loadPlatforms() {
            try {
                const response = await axios.get('/ai-config/platforms');
                platforms = response.data.data;
                renderPlatforms();
                populatePlatformSelect();
            } catch (error) {
                console.error('加载平台列表失败:', error);
            }
        }

        // 加载配置列表
        async function loadConfigurations() {
            try {
                const response = await axios.get('/ai-config/configurations');
                configurations = response.data.data;
                renderConfigurations();
            } catch (error) {
                console.error('加载配置列表失败:', error);
            }
        }

        // 检查当前配置状态
        async function checkCurrentConfig() {
            try {
                // 这里可以添加检查当前配置状态的API调用
                const defaultConfig = configurations.find(config => config.is_default);
                if (defaultConfig) {
                    document.getElementById('current-config').innerHTML = `
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                已配置
                            </span>
                            <span>默认配置: ${defaultConfig.config_name}</span>
                            <span>平台: ${defaultConfig.platform?.platform_name}</span>
                            <span>模型: ${defaultConfig.model?.model_name}</span>
                        </div>
                    `;
                } else {
                    document.getElementById('current-config').innerHTML = `
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            未配置默认AI服务
                        </span>
                    `;
                }
            } catch (error) {
                console.error('检查配置状态失败:', error);
            }
        }

        // 渲染平台列表
        function renderPlatforms() {
            const platformList = document.getElementById('platform-list');
            platformList.innerHTML = platforms.map(platform => `
                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                        <span class="font-medium">${platform.platform_name}</span>
                        <span class="text-sm text-gray-500 ml-2">(${platform.platform_code})</span>
                    </div>
                    <button onclick="loadModels(${platform.platform_id})" class="text-blue-500 hover:text-blue-600 text-sm">
                        查看模型
                    </button>
                </div>
            `).join('');
        }

        // 渲染配置列表
        function renderConfigurations() {
            const configList = document.getElementById('config-list');
            if (configurations.length === 0) {
                configList.innerHTML = '<p class="text-gray-500">暂无配置</p>';
                return;
            }

            configList.innerHTML = configurations.map(config => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium">${config.config_name}</span>
                            ${config.is_default ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">默认</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500 mt-1">
                            ${config.platform?.platform_name} - ${config.model?.model_name}
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        ${!config.is_default ? `<button onclick="setDefault(${config.config_id})" class="text-blue-500 hover:text-blue-600 text-sm">设为默认</button>` : ''}
                        <button onclick="testConfig('${config.config_name}')" class="text-green-500 hover:text-green-600 text-sm">测试</button>
                        <button onclick="editConfig(${config.config_id})" class="text-yellow-500 hover:text-yellow-600 text-sm">编辑</button>
                        ${!config.is_default ? `<button onclick="deleteConfig(${config.config_id})" class="text-red-500 hover:text-red-600 text-sm">删除</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 加载指定平台的模型
        async function loadModels(platformId) {
            try {
                const response = await axios.get(`/ai-config/models?platform_id=${platformId}`);
                models = response.data.data;
                renderModels();
            } catch (error) {
                console.error('加载模型列表失败:', error);
            }
        }

        // 渲染模型列表
        function renderModels() {
            const modelList = document.getElementById('model-list');
            modelList.innerHTML = models.map(model => `
                <div class="p-3 border border-gray-200 rounded-lg">
                    <div class="font-medium">${model.model_name}</div>
                    <div class="text-sm text-gray-500">${model.model_code}</div>
                    <div class="text-xs text-gray-400 mt-1">
                        最大Token: ${model.max_tokens || 'N/A'} | 
                        流式输出: ${model.supports_streaming ? '支持' : '不支持'}
                    </div>
                </div>
            `).join('');
        }

        // 填充平台选择框
        function populatePlatformSelect() {
            const select = document.getElementById('platform-select');
            select.innerHTML = '<option value="">选择平台</option>' + 
                platforms.map(platform => `<option value="${platform.platform_id}">${platform.platform_name}</option>`).join('');
        }

        // 平台选择变化时更新模型选择框
        document.getElementById('platform-select').addEventListener('change', async function() {
            const platformId = this.value;
            const modelSelect = document.getElementById('model-select');
            
            if (!platformId) {
                modelSelect.innerHTML = '<option value="">选择模型</option>';
                return;
            }

            try {
                const response = await axios.get(`/ai-config/models?platform_id=${platformId}`);
                const platformModels = response.data.data;
                modelSelect.innerHTML = '<option value="">选择模型</option>' + 
                    platformModels.map(model => `<option value="${model.model_id}">${model.model_name}</option>`).join('');
            } catch (error) {
                console.error('加载模型失败:', error);
            }
        });

        // 显示创建模态框
        function showCreateModal() {
            document.getElementById('modal-title').textContent = '新增AI配置';
            document.getElementById('config-form').reset();
            document.getElementById('config-modal').classList.remove('hidden');
            document.getElementById('config-modal').classList.add('flex');
        }

        // 隐藏模态框
        function hideModal() {
            document.getElementById('config-modal').classList.add('hidden');
            document.getElementById('config-modal').classList.remove('flex');
        }

        // 配置表单提交
        document.getElementById('config-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                config_name: document.getElementById('config-name').value,
                platform_id: document.getElementById('platform-select').value,
                model_id: document.getElementById('model-select').value,
                api_key: document.getElementById('api-key').value,
                api_endpoint: document.getElementById('api-endpoint').value,
                temperature: parseFloat(document.getElementById('temperature').value),
                is_default: document.getElementById('is-default').checked
            };

            try {
                await axios.post('/ai-config/configurations', formData);
                hideModal();
                loadConfigurations();
                checkCurrentConfig();
                alert('配置创建成功！');
            } catch (error) {
                console.error('创建配置失败:', error);
                alert('创建配置失败: ' + (error.response?.data?.message || error.message));
            }
        });

        // 设置默认配置
        async function setDefault(configId) {
            try {
                await axios.post('/ai-config/set-default', { config_id: configId });
                loadConfigurations();
                checkCurrentConfig();
                alert('默认配置设置成功！');
            } catch (error) {
                console.error('设置默认配置失败:', error);
                alert('设置默认配置失败: ' + (error.response?.data?.message || error.message));
            }
        }

        // 测试配置
        async function testConfig(configName) {
            try {
                const response = await axios.post('/ai-config/test-config', { config_name: configName });
                alert('配置测试成功！AI响应: ' + response.data.data.response);
            } catch (error) {
                console.error('配置测试失败:', error);
                alert('配置测试失败: ' + (error.response?.data?.message || error.message));
            }
        }

        // 删除配置
        async function deleteConfig(configId) {
            if (!confirm('确定要删除这个配置吗？')) {
                return;
            }

            try {
                await axios.delete(`/ai-config/configurations?config_id=${configId}`);
                loadConfigurations();
                checkCurrentConfig();
                alert('配置删除成功！');
            } catch (error) {
                console.error('删除配置失败:', error);
                alert('删除配置失败: ' + (error.response?.data?.message || error.message));
            }
        }
    </script>
</body>
</html>
