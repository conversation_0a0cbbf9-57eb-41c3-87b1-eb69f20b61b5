<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型切换器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100">
    <!-- 通知组件 -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border-l-4 border-blue-500 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex">
                <div class="flex-shrink-0">
                    <div id="notification-icon" class="w-5 h-5"></div>
                </div>
                <div class="ml-3">
                    <p id="notification-message" class="text-sm text-gray-700"></p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="hideNotification()" class="text-gray-400 hover:text-gray-600">
                        <span class="sr-only">关闭</span>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">AI模型切换器</h1>
            <div class="flex space-x-2">
                <a href="/ai-config/enhanced" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    配置管理
                </a>
                <button onclick="refreshData()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                    刷新
                </button>
            </div>
        </div>

        <!-- 当前配置状态 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">当前AI配置</h2>
            <div id="current-config" class="space-y-2">
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div class="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
                </div>
            </div>
        </div>

        <!-- 配置统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">总</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">总配置数</p>
                        <p id="total-configs" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">启</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">启用配置</p>
                        <p id="active-configs" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">停</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">停用配置</p>
                        <p id="inactive-configs" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">平</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">平台数量</p>
                        <p id="platform-count" class="text-2xl font-semibold text-gray-900">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速切换区域 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">快速切换AI配置</h2>
            <div class="flex items-center space-x-4">
                <select id="config-selector" class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">选择AI配置...</option>
                </select>
                <button onclick="switchConfiguration()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg">
                    切换
                </button>
            </div>
        </div>

        <!-- 可用配置列表 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-700">可用AI配置</h2>
                <div class="flex space-x-2">
                    <select id="platform-filter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option value="">所有平台</option>
                    </select>
                    <input type="text" id="search-input" placeholder="搜索配置..." class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                </div>
            </div>
            <div id="configurations-list" class="space-y-4">
                <div class="animate-pulse space-y-4">
                    <div class="h-16 bg-gray-200 rounded"></div>
                    <div class="h-16 bg-gray-200 rounded"></div>
                    <div class="h-16 bg-gray-200 rounded"></div>
                </div>
            </div>
        </div>

        <!-- 平台统计 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">按平台统计</h3>
                <div id="platform-stats" class="space-y-2">
                    <div class="animate-pulse space-y-2">
                        <div class="h-4 bg-gray-200 rounded"></div>
                        <div class="h-4 bg-gray-200 rounded"></div>
                        <div class="h-4 bg-gray-200 rounded"></div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">按模型统计</h3>
                <div id="model-stats" class="space-y-2">
                    <div class="animate-pulse space-y-2">
                        <div class="h-4 bg-gray-200 rounded"></div>
                        <div class="h-4 bg-gray-200 rounded"></div>
                        <div class="h-4 bg-gray-200 rounded"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let configurations = [];
        let stats = {};
        let currentConfig = null;

        // 环境检测和API基础路径
        function getApiBasePath() {
            const currentPath = window.location.pathname;
            if (currentPath.includes('/ai/')) {
                return '/ai/ai-config';
            }
            return '/ai-config';
        }

        const API_BASE_PATH = getApiBasePath();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 搜索功能
            document.getElementById('search-input').addEventListener('input', function() {
                filterConfigurations();
            });

            // 平台过滤
            document.getElementById('platform-filter').addEventListener('change', function() {
                filterConfigurations();
            });
        }

        // 加载所有数据
        async function loadData() {
            await Promise.all([
                loadCurrentConfig(),
                loadActiveConfigurations(),
                loadStats()
            ]);
        }

        // 刷新数据
        async function refreshData() {
            showNotification('正在刷新数据...', 'info');
            await loadData();
            showNotification('数据刷新完成！', 'success');
        }

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const messageEl = document.getElementById('notification-message');
            const iconEl = document.getElementById('notification-icon');

            messageEl.textContent = message;

            const colors = {
                success: 'border-green-500',
                error: 'border-red-500',
                warning: 'border-yellow-500',
                info: 'border-blue-500'
            };

            const icons = {
                success: '✓',
                error: '✗',
                warning: '⚠',
                info: 'ℹ'
            };

            notification.className = `fixed top-4 right-4 z-50 ${colors[type] || colors.info}`;
            iconEl.textContent = icons[type] || icons.info;

            notification.classList.remove('hidden');

            setTimeout(() => {
                hideNotification();
            }, 3000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        // 加载当前配置
        async function loadCurrentConfig() {
            try {
                const response = await axios.get(`${API_BASE_PATH}/current-config`);
                currentConfig = response.data.data;
                renderCurrentConfig();
            } catch (error) {
                console.error('加载当前配置失败:', error);
                document.getElementById('current-config').innerHTML = `
                    <div class="text-red-500">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            未配置默认AI服务
                        </span>
                    </div>
                `;
            }
        }

        // 渲染当前配置
        function renderCurrentConfig() {
            const container = document.getElementById('current-config');
            if (!currentConfig) {
                container.innerHTML = `
                    <div class="text-red-500">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            未配置默认AI服务
                        </span>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            当前默认
                        </span>
                        <div>
                            <div class="font-medium text-lg">${currentConfig.config_name}</div>
                            <div class="text-sm text-gray-500">
                                ${currentConfig.platform_name} - ${currentConfig.model_name}
                            </div>
                        </div>
                    </div>
                    <div class="text-right text-sm text-gray-500">
                        <div>温度: ${currentConfig.temperature}</div>
                        <div>最大Token: ${currentConfig.max_tokens || 'N/A'}</div>
                    </div>
                </div>
            `;
        }

        // 加载启用的配置列表
        async function loadActiveConfigurations() {
            try {
                const response = await axios.get(`${API_BASE_PATH}/active-configurations`);
                configurations = response.data.data;
                renderConfigurations();
                populateConfigSelector();
                populatePlatformFilter();
            } catch (error) {
                console.error('加载配置列表失败:', error);
                showNotification('加载配置列表失败', 'error');
            }
        }

        // 渲染配置列表
        function renderConfigurations(configsToRender = configurations) {
            const container = document.getElementById('configurations-list');

            if (configsToRender.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">暂无可用配置</p>';
                return;
            }

            container.innerHTML = configsToRender.map(config => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 ${config.is_default ? 'ring-2 ring-blue-500 bg-blue-50' : ''}">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium">${config.config_name}</span>
                            ${config.is_default ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">当前默认</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500 mt-1">
                            ${config.platform_name} - ${config.model_name}
                        </div>
                        <div class="text-xs text-gray-400 mt-1">
                            温度: ${config.temperature} | 最大Token: ${config.max_tokens || 'N/A'}
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        ${!config.is_default ? `
                            <button onclick="quickSwitch(${config.config_id})" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm">
                                设为默认
                            </button>
                        ` : `
                            <span class="text-blue-600 text-sm font-medium">当前使用</span>
                        `}
                    </div>
                </div>
            `).join('');
        }

        // 填充配置选择器
        function populateConfigSelector() {
            const selector = document.getElementById('config-selector');
            selector.innerHTML = '<option value="">选择AI配置...</option>' +
                configurations.map(config => `
                    <option value="${config.config_id}" ${config.is_default ? 'selected' : ''}>
                        ${config.display_name}
                    </option>
                `).join('');
        }

        // 填充平台过滤器
        function populatePlatformFilter() {
            const platforms = [...new Set(configurations.map(config => config.platform_name))];
            const filter = document.getElementById('platform-filter');
            filter.innerHTML = '<option value="">所有平台</option>' +
                platforms.map(platform => `<option value="${platform}">${platform}</option>`).join('');
        }

        // 过滤配置
        function filterConfigurations() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const platformFilter = document.getElementById('platform-filter').value;

            let filtered = configurations;

            if (searchTerm) {
                filtered = filtered.filter(config =>
                    config.config_name.toLowerCase().includes(searchTerm) ||
                    config.platform_name.toLowerCase().includes(searchTerm) ||
                    config.model_name.toLowerCase().includes(searchTerm)
                );
            }

            if (platformFilter) {
                filtered = filtered.filter(config => config.platform_name === platformFilter);
            }

            renderConfigurations(filtered);
        }

        // 快速切换配置
        async function quickSwitch(configId) {
            try {
                const response = await axios.post(`${API_BASE_PATH}/switch-config`, {
                    config_id: configId
                });

                showNotification('配置切换成功！', 'success');
                await loadData(); // 重新加载数据
            } catch (error) {
                console.error('切换配置失败:', error);
                showNotification('切换配置失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 通过选择器切换配置
        async function switchConfiguration() {
            const configId = document.getElementById('config-selector').value;
            if (!configId) {
                showNotification('请选择要切换的配置', 'warning');
                return;
            }

            await quickSwitch(configId);
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await axios.get(`${API_BASE_PATH}/config-stats`);
                stats = response.data.data;
                renderStats();
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 渲染统计信息
        function renderStats() {
            document.getElementById('total-configs').textContent = stats.total_configs || 0;
            document.getElementById('active-configs').textContent = stats.active_configs || 0;
            document.getElementById('inactive-configs').textContent = stats.inactive_configs || 0;
            document.getElementById('platform-count').textContent = stats.platform_stats?.length || 0;

            // 平台统计
            const platformStatsContainer = document.getElementById('platform-stats');
            if (stats.platform_stats && stats.platform_stats.length > 0) {
                platformStatsContainer.innerHTML = stats.platform_stats.map(stat => `
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-sm text-gray-600">${stat.platform_name}</span>
                        <span class="text-sm font-medium text-gray-900">${stat.count} 个配置</span>
                    </div>
                `).join('');
            } else {
                platformStatsContainer.innerHTML = '<p class="text-gray-500 text-sm">暂无数据</p>';
            }

            // 模型统计
            const modelStatsContainer = document.getElementById('model-stats');
            if (stats.model_stats && stats.model_stats.length > 0) {
                modelStatsContainer.innerHTML = stats.model_stats.map(stat => `
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="text-sm text-gray-600">${stat.model_name}</span>
                        <span class="text-sm font-medium text-gray-900">${stat.count} 个配置</span>
                    </div>
                `).join('');
            } else {
                modelStatsContainer.innerHTML = '<p class="text-gray-500 text-sm">暂无数据</p>';
            }
        }
    </script>
</body>
</html>
