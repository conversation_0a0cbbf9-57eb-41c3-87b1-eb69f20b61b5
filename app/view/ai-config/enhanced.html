<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI配置管理增强版 - 专业的AI模型配置管理工具，支持多平台模型配置和高级功能">
    <meta name="keywords" content="AI配置,模型管理,AI平台,配置管理,人工智能">
    <title>AI配置管理 - 增强版 | AI对话系统</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://unpkg.com">

    <!-- External dependencies -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <!-- Common styles -->
    <link rel="stylesheet" href="../assets/css/common.css">
</head>
<body class="bg-gray-100">
    <!-- 通知组件 -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border-l-4 border-blue-500 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex">
                <div class="flex-shrink-0">
                    <div id="notification-icon" class="w-5 h-5"></div>
                </div>
                <div class="ml-3">
                    <p id="notification-message" class="text-sm text-gray-700"></p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="hideNotification()" class="text-gray-400 hover:text-gray-600">
                        <span class="sr-only">关闭</span>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b" role="navigation" aria-label="主导航">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-4">
                    <a href="../index" class="text-blue-600 hover:text-blue-800 font-medium">
                        ← 返回首页
                    </a>
                    <span class="text-gray-300">|</span>
                    <span class="text-gray-600">AI配置管理</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="../dialogue/generator" class="text-gray-600 hover:text-gray-800">对话生成</a>
                    <a href="../dialogue/quality" class="text-gray-600 hover:text-gray-800">质量检查</a>
                </div>
            </div>
        </div>
    </nav>

    <main class="container mx-auto px-4 py-8" role="main">
        <header class="flex flex-col md:flex-row md:justify-between md:items-center mb-8 space-y-4 md:space-y-0">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">AI配置管理</h1>
                <p class="text-gray-600 mt-1">管理AI模型配置，支持多平台和高级功能</p>
            </div>
            <div class="flex flex-wrap gap-2">
                <a href="switcher" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
                    模型切换器
                </a>
                <button onclick="showBatchModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    批量操作
                </button>
                <button onclick="exportConfigs()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                    导出配置
                </button>
            </div>
        </header>
        
        <!-- 当前配置状态 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">当前配置状态</h2>
            <div id="current-config" class="text-gray-600">
                加载中...
            </div>
        </div>

        <!-- 配置列表 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-700">AI配置列表</h2>
                <div class="flex space-x-2">
                    <input type="text" id="search-input" placeholder="搜索配置..." class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                    <button onclick="showCreateModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                        新增配置
                    </button>
                </div>
            </div>
            <div id="config-list" class="space-y-4">
                加载中...
            </div>
        </div>

        <!-- 平台和模型信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- 平台列表 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">支持的AI平台</h2>
                <div id="platform-list" class="space-y-2">
                    加载中...
                </div>
            </div>

            <!-- 模型列表 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">可用模型</h2>
                <div id="model-list" class="space-y-2">
                    选择平台查看模型...
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑配置模态框 -->
    <div id="config-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-40">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 max-h-screen overflow-y-auto">
            <h3 id="modal-title" class="text-lg font-semibold text-gray-800 mb-4">新增AI配置</h3>
            <form id="config-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">配置名称</label>
                    <input type="text" id="config-name" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">AI平台</label>
                    <select id="platform-select" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                        <option value="">选择平台</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">AI模型</label>
                    <select id="model-select" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                        <option value="">选择模型</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API密钥</label>
                    <input type="password" id="api-key" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API端点 (可选)</label>
                    <input type="url" id="api-endpoint" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">温度参数</label>
                        <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.8" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">最大Token</label>
                        <input type="number" id="max-tokens" min="1" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">频率惩罚</label>
                        <input type="number" id="frequency-penalty" min="-2" max="2" step="0.1" value="0" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">存在惩罚</label>
                        <input type="number" id="presence-penalty" min="-2" max="2" step="0.1" value="0" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                    </div>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="is-default" class="mr-2">
                    <label for="is-default" class="text-sm text-gray-700">设为默认配置</label>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 高级测试模态框 -->
    <div id="test-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-40">
        <div class="bg-white rounded-lg p-8 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">高级配置测试</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">测试消息</label>
                    <textarea id="test-message" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="输入要测试的消息...">你好，请回复"测试成功"</textarea>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">临时温度参数</label>
                        <input type="number" id="test-temperature" min="0" max="2" step="0.1" class="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="使用配置默认值">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">临时最大Token</label>
                        <input type="number" id="test-max-tokens" min="1" class="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="使用配置默认值">
                    </div>
                </div>
                <div id="test-result" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-1">测试结果</label>
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div id="test-response" class="text-sm text-gray-800 mb-2"></div>
                        <div id="test-info" class="text-xs text-gray-500"></div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideTestModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        关闭
                    </button>
                    <button type="button" onclick="runAdvancedTest()" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                        <span id="test-btn-text">开始测试</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量操作模态框 -->
    <div id="batch-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-40">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">批量操作</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择操作</label>
                    <select id="batch-operation" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                        <option value="">选择操作</option>
                        <option value="activate">启用配置</option>
                        <option value="deactivate">停用配置</option>
                        <option value="delete">删除配置</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择配置</label>
                    <div id="batch-config-list" class="max-h-48 overflow-y-auto border border-gray-200 rounded-lg p-2">
                        <!-- 配置列表将在这里动态生成 -->
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideBatchModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" onclick="executeBatchOperation()" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600">
                        执行操作
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let platforms = [];
        let models = [];
        let configurations = [];
        let currentTestConfig = null;

        // 环境检测和API基础路径
        function getApiBasePath() {
            const currentPath = window.location.pathname;
            // 如果当前路径包含 /ai/，说明是生产环境
            if (currentPath.includes('/ai/')) {
                return '/ai/ai-config';
            }
            // 否则是测试环境
            return '/ai-config';
        }

        const API_BASE_PATH = getApiBasePath();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPlatforms();
            loadConfigurations();
            checkCurrentConfig();
            setupSearchFilter();
        });

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const messageEl = document.getElementById('notification-message');
            const iconEl = document.getElementById('notification-icon');
            
            messageEl.textContent = message;
            
            // 设置图标和颜色
            const colors = {
                success: 'border-green-500',
                error: 'border-red-500',
                warning: 'border-yellow-500',
                info: 'border-blue-500'
            };
            
            const icons = {
                success: '✓',
                error: '✗',
                warning: '⚠',
                info: 'ℹ'
            };
            
            notification.className = `fixed top-4 right-4 z-50 ${colors[type] || colors.info}`;
            iconEl.textContent = icons[type] || icons.info;
            
            notification.classList.remove('hidden');
            
            // 3秒后自动隐藏
            setTimeout(() => {
                hideNotification();
            }, 3000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        // 搜索过滤功能
        function setupSearchFilter() {
            const searchInput = document.getElementById('search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                filterConfigurations(searchTerm);
            });
        }

        function filterConfigurations(searchTerm) {
            const filteredConfigs = configurations.filter(config =>
                config.config_name.toLowerCase().includes(searchTerm) ||
                config.platform?.platform_name.toLowerCase().includes(searchTerm) ||
                config.model?.model_name.toLowerCase().includes(searchTerm)
            );
            renderConfigurations(filteredConfigs);
        }

        // 加载平台列表
        async function loadPlatforms() {
            try {
                const response = await axios.get(`${API_BASE_PATH}/platforms`);
                platforms = response.data.data;
                renderPlatforms();
                populatePlatformSelect();
            } catch (error) {
                console.error('加载平台列表失败:', error);
                showNotification('加载平台列表失败', 'error');
            }
        }

        // 加载配置列表
        async function loadConfigurations() {
            try {
                const response = await axios.get(`${API_BASE_PATH}/configurations`);
                configurations = response.data.data;
                renderConfigurations();
            } catch (error) {
                console.error('加载配置列表失败:', error);
                showNotification('加载配置列表失败', 'error');
            }
        }

        // 检查当前配置状态
        async function checkCurrentConfig() {
            try {
                const defaultConfig = configurations.find(config => config.is_default);
                if (defaultConfig) {
                    document.getElementById('current-config').innerHTML = `
                        <div class="flex items-center space-x-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                已配置
                            </span>
                            <span>默认配置: ${defaultConfig.config_name}</span>
                            <span>平台: ${defaultConfig.platform?.platform_name}</span>
                            <span>模型: ${defaultConfig.model?.model_name}</span>
                        </div>
                    `;
                } else {
                    document.getElementById('current-config').innerHTML = `
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            未配置默认AI服务
                        </span>
                    `;
                }
            } catch (error) {
                console.error('检查配置状态失败:', error);
            }
        }

        // 渲染平台列表
        function renderPlatforms() {
            const platformList = document.getElementById('platform-list');
            platformList.innerHTML = platforms.map(platform => `
                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                    <div>
                        <span class="font-medium">${platform.platform_name}</span>
                        <span class="text-sm text-gray-500 ml-2">(${platform.platform_code})</span>
                    </div>
                    <button onclick="loadModels(${platform.platform_id})" class="text-blue-500 hover:text-blue-600 text-sm">
                        查看模型
                    </button>
                </div>
            `).join('');
        }

        // 渲染配置列表
        function renderConfigurations(configsToRender = configurations) {
            const configList = document.getElementById('config-list');
            if (configsToRender.length === 0) {
                configList.innerHTML = '<p class="text-gray-500">暂无配置</p>';
                return;
            }

            configList.innerHTML = configsToRender.map(config => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium">${config.config_name}</span>
                            ${config.is_default ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">默认</span>' : ''}
                            ${!config.is_active ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">已停用</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500 mt-1">
                            ${config.platform?.platform_name} - ${config.model?.model_name}
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        ${!config.is_default ? `<button onclick="setDefault(${config.config_id})" class="text-blue-500 hover:text-blue-600 text-sm">设为默认</button>` : ''}
                        <button onclick="showAdvancedTest('${config.config_name}')" class="text-green-500 hover:text-green-600 text-sm">测试</button>
                        <button onclick="editConfig(${config.config_id})" class="text-yellow-500 hover:text-yellow-600 text-sm">编辑</button>
                        <button onclick="copyConfig(${config.config_id})" class="text-purple-500 hover:text-purple-600 text-sm">复制</button>
                        ${!config.is_default ? `<button onclick="deleteConfig(${config.config_id})" class="text-red-500 hover:text-red-600 text-sm">删除</button>` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 加载指定平台的模型
        async function loadModels(platformId) {
            try {
                const response = await axios.get(`${API_BASE_PATH}/models?platform_id=${platformId}`);
                models = response.data.data;
                renderModels();
            } catch (error) {
                console.error('加载模型列表失败:', error);
                showNotification('加载模型列表失败', 'error');
            }
        }

        // 渲染模型列表
        function renderModels() {
            const modelList = document.getElementById('model-list');
            modelList.innerHTML = models.map(model => `
                <div class="p-3 border border-gray-200 rounded-lg">
                    <div class="font-medium">${model.model_name}</div>
                    <div class="text-sm text-gray-500">${model.model_code}</div>
                    <div class="text-xs text-gray-400 mt-1">
                        最大Token: ${model.max_tokens || 'N/A'} |
                        流式输出: ${model.supports_streaming ? '支持' : '不支持'}
                    </div>
                </div>
            `).join('');
        }

        // 填充平台选择框
        function populatePlatformSelect() {
            const select = document.getElementById('platform-select');
            select.innerHTML = '<option value="">选择平台</option>' +
                platforms.map(platform => `<option value="${platform.platform_id}">${platform.platform_name}</option>`).join('');
        }

        // 平台选择变化时更新模型选择框
        document.getElementById('platform-select').addEventListener('change', async function() {
            const platformId = this.value;
            const modelSelect = document.getElementById('model-select');

            if (!platformId) {
                modelSelect.innerHTML = '<option value="">选择模型</option>';
                return;
            }

            try {
                const response = await axios.get(`${API_BASE_PATH}/models?platform_id=${platformId}`);
                const platformModels = response.data.data;
                modelSelect.innerHTML = '<option value="">选择模型</option>' +
                    platformModels.map(model => `<option value="${model.model_id}">${model.model_name}</option>`).join('');
            } catch (error) {
                console.error('加载模型失败:', error);
                showNotification('加载模型失败', 'error');
            }
        });

        // 显示创建模态框
        function showCreateModal() {
            document.getElementById('modal-title').textContent = '新增AI配置';
            document.getElementById('config-form').reset();
            document.getElementById('config-form').dataset.mode = 'create';
            delete document.getElementById('config-form').dataset.configId;
            document.getElementById('config-modal').classList.remove('hidden');
            document.getElementById('config-modal').classList.add('flex');
        }

        // 隐藏模态框
        function hideModal() {
            document.getElementById('config-modal').classList.add('hidden');
            document.getElementById('config-modal').classList.remove('flex');
        }

        // 编辑配置
        async function editConfig(configId) {
            try {
                const response = await axios.get(`${API_BASE_PATH}/config?config_id=${configId}`);
                const config = response.data.data;

                // 填充编辑表单
                document.getElementById('modal-title').textContent = '编辑AI配置';
                document.getElementById('config-name').value = config.config_name;
                document.getElementById('platform-select').value = config.platform_id;

                // 加载对应平台的模型
                await loadModelsForEdit(config.platform_id, config.model_id);

                document.getElementById('api-key').value = config.api_key;
                document.getElementById('api-endpoint').value = config.api_endpoint || '';
                document.getElementById('temperature').value = config.temperature;
                document.getElementById('max-tokens').value = config.max_tokens || '';
                document.getElementById('frequency-penalty').value = config.frequency_penalty || 0;
                document.getElementById('presence-penalty').value = config.presence_penalty || 0;
                document.getElementById('is-default').checked = config.is_default;

                // 设置表单为编辑模式
                document.getElementById('config-form').dataset.mode = 'edit';
                document.getElementById('config-form').dataset.configId = configId;

                // 显示模态框
                document.getElementById('config-modal').classList.remove('hidden');
                document.getElementById('config-modal').classList.add('flex');
            } catch (error) {
                console.error('获取配置详情失败:', error);
                showNotification('获取配置详情失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 为编辑加载模型列表
        async function loadModelsForEdit(platformId, selectedModelId) {
            try {
                const response = await axios.get(`${API_BASE_PATH}/models?platform_id=${platformId}`);
                const models = response.data.data;
                const modelSelect = document.getElementById('model-select');

                modelSelect.innerHTML = '<option value="">选择模型</option>' +
                    models.map(model => `<option value="${model.model_id}" ${model.model_id == selectedModelId ? 'selected' : ''}>${model.model_name}</option>`).join('');
            } catch (error) {
                console.error('加载模型失败:', error);
                showNotification('加载模型失败', 'error');
            }
        }

        // 配置表单提交
        document.getElementById('config-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const mode = this.dataset.mode;
            const configId = this.dataset.configId;

            const formData = {
                config_name: document.getElementById('config-name').value,
                platform_id: document.getElementById('platform-select').value,
                model_id: document.getElementById('model-select').value,
                api_key: document.getElementById('api-key').value,
                api_endpoint: document.getElementById('api-endpoint').value,
                temperature: parseFloat(document.getElementById('temperature').value),
                max_tokens: document.getElementById('max-tokens').value ? parseInt(document.getElementById('max-tokens').value) : null,
                frequency_penalty: parseFloat(document.getElementById('frequency-penalty').value),
                presence_penalty: parseFloat(document.getElementById('presence-penalty').value),
                is_default: document.getElementById('is-default').checked
            };

            try {
                if (mode === 'edit') {
                    await axios.put(`${API_BASE_PATH}/configurations?config_id=${configId}`, formData);
                    showNotification('配置更新成功！', 'success');
                } else {
                    await axios.post(`${API_BASE_PATH}/configurations`, formData);
                    showNotification('配置创建成功！', 'success');
                }

                hideModal();
                loadConfigurations();
                checkCurrentConfig();
            } catch (error) {
                console.error('保存配置失败:', error);
                showNotification('保存配置失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        });

        // 设置默认配置
        async function setDefault(configId) {
            try {
                await axios.post(`${API_BASE_PATH}/set-default`, { config_id: configId });
                loadConfigurations();
                checkCurrentConfig();
                showNotification('默认配置设置成功！', 'success');
            } catch (error) {
                console.error('设置默认配置失败:', error);
                showNotification('设置默认配置失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 删除配置
        async function deleteConfig(configId) {
            if (!confirm('确定要删除这个配置吗？')) {
                return;
            }

            try {
                await axios.delete(`${API_BASE_PATH}/configurations?config_id=${configId}`);
                loadConfigurations();
                checkCurrentConfig();
                showNotification('配置删除成功！', 'success');
            } catch (error) {
                console.error('删除配置失败:', error);
                showNotification('删除配置失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 复制配置
        async function copyConfig(configId) {
            const newName = prompt('请输入新配置的名称:');
            if (!newName) return;

            try {
                await axios.post(`${API_BASE_PATH}/copy-config`, {
                    config_id: configId,
                    new_name: newName
                });
                loadConfigurations();
                showNotification('配置复制成功！', 'success');
            } catch (error) {
                console.error('复制配置失败:', error);
                showNotification('复制配置失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 显示高级测试模态框
        function showAdvancedTest(configName) {
            currentTestConfig = configName;
            document.getElementById('test-result').classList.add('hidden');
            document.getElementById('test-message').value = '你好，请回复"测试成功"';
            document.getElementById('test-temperature').value = '';
            document.getElementById('test-max-tokens').value = '';
            document.getElementById('test-btn-text').textContent = '开始测试';
            document.getElementById('test-modal').classList.remove('hidden');
            document.getElementById('test-modal').classList.add('flex');
        }

        // 隐藏测试模态框
        function hideTestModal() {
            document.getElementById('test-modal').classList.add('hidden');
            document.getElementById('test-modal').classList.remove('flex');
        }

        // 运行高级测试
        async function runAdvancedTest() {
            if (!currentTestConfig) return;

            const testBtn = document.getElementById('test-btn-text');
            testBtn.textContent = '测试中...';

            const testData = {
                config_name: currentTestConfig,
                test_message: document.getElementById('test-message').value,
                temperature: document.getElementById('test-temperature').value || null,
                max_tokens: document.getElementById('test-max-tokens').value || null
            };

            try {
                const response = await axios.post(`${API_BASE_PATH}/advanced-test`, testData);
                const result = response.data.data;

                document.getElementById('test-response').textContent = result.response;
                document.getElementById('test-info').innerHTML = `
                    响应时间: ${result.response_time}ms |
                    测试消息: "${result.test_message}" |
                    配置: ${result.config.model}
                `;
                document.getElementById('test-result').classList.remove('hidden');
                showNotification('配置测试成功！', 'success');
            } catch (error) {
                console.error('配置测试失败:', error);
                showNotification('配置测试失败: ' + (error.response?.data?.message || error.message), 'error');
            } finally {
                testBtn.textContent = '开始测试';
            }
        }

        // 显示批量操作模态框
        function showBatchModal() {
            populateBatchConfigList();
            document.getElementById('batch-modal').classList.remove('hidden');
            document.getElementById('batch-modal').classList.add('flex');
        }

        // 隐藏批量操作模态框
        function hideBatchModal() {
            document.getElementById('batch-modal').classList.add('hidden');
            document.getElementById('batch-modal').classList.remove('flex');
        }

        // 填充批量操作配置列表
        function populateBatchConfigList() {
            const configList = document.getElementById('batch-config-list');
            configList.innerHTML = configurations.map(config => `
                <div class="flex items-center space-x-2 p-2 hover:bg-gray-50">
                    <input type="checkbox" id="batch-config-${config.config_id}" value="${config.config_id}" class="batch-config-checkbox">
                    <label for="batch-config-${config.config_id}" class="flex-1 text-sm">
                        ${config.config_name}
                        ${config.is_default ? '<span class="text-green-600">(默认)</span>' : ''}
                        ${!config.is_active ? '<span class="text-gray-500">(已停用)</span>' : ''}
                    </label>
                </div>
            `).join('');
        }

        // 执行批量操作
        async function executeBatchOperation() {
            const operation = document.getElementById('batch-operation').value;
            const checkboxes = document.querySelectorAll('.batch-config-checkbox:checked');
            const configIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

            if (!operation) {
                showNotification('请选择操作类型', 'warning');
                return;
            }

            if (configIds.length === 0) {
                showNotification('请选择要操作的配置', 'warning');
                return;
            }

            if (!confirm(`确定要对 ${configIds.length} 个配置执行"${document.getElementById('batch-operation').selectedOptions[0].text}"操作吗？`)) {
                return;
            }

            try {
                const response = await axios.post(`${API_BASE_PATH}/batch-operation`, {
                    operation: operation,
                    config_ids: configIds
                });

                const result = response.data.data;
                let message = response.data.message;

                if (result.errors && result.errors.length > 0) {
                    message += '\n错误信息:\n' + result.errors.join('\n');
                    showNotification(message, 'warning');
                } else {
                    showNotification(message, 'success');
                }

                hideBatchModal();
                loadConfigurations();
                checkCurrentConfig();
            } catch (error) {
                console.error('批量操作失败:', error);
                showNotification('批量操作失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 导出配置
        function exportConfigs() {
            const exportData = {
                export_time: new Date().toISOString(),
                configurations: configurations.map(config => ({
                    config_name: config.config_name,
                    platform_name: config.platform?.platform_name,
                    platform_code: config.platform?.platform_code,
                    model_name: config.model?.model_name,
                    model_code: config.model?.model_code,
                    api_endpoint: config.api_endpoint,
                    temperature: config.temperature,
                    max_tokens: config.max_tokens,
                    frequency_penalty: config.frequency_penalty,
                    presence_penalty: config.presence_penalty,
                    is_default: config.is_default,
                    is_active: config.is_active
                    // 注意：不导出API密钥以保证安全
                }))
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `ai-configs-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('配置导出成功！', 'success');
        }

        // 简单测试配置（兼容原有功能）
        async function testConfig(configName) {
            try {
                const response = await axios.post(`${API_BASE_PATH}/test-config`, { config_name: configName });
                showNotification('配置测试成功！AI响应: ' + response.data.data.response, 'success');
            } catch (error) {
                console.error('配置测试失败:', error);
                showNotification('配置测试失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }
    </script>
</body>
</html>
