<?php

namespace app\model;

use support\Model;

/**
 * ai_configurations AI配置表
 * @property integer $config_id 配置ID(主键)
 * @property string $config_name 配置名称
 * @property integer $platform_id 平台ID
 * @property integer $model_id 模型ID
 * @property string $api_key API密钥
 * @property string $api_endpoint API端点
 * @property float $temperature 温度参数
 * @property integer $max_tokens 最大输出token数
 * @property float $frequency_penalty 频率惩罚
 * @property float $presence_penalty 存在惩罚
 * @property integer $is_default 是否为默认配置
 * @property integer $is_active 是否启用
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class AiConfigurations extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ai_configurations';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'config_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'config_name',
        'platform_id',
        'model_id',
        'api_key',
        'api_endpoint',
        'temperature',
        'max_tokens',
        'frequency_penalty',
        'presence_penalty',
        'is_default',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'platform_id' => 'integer',
        'model_id' => 'integer',
        'temperature' => 'decimal:2',
        'max_tokens' => 'integer',
        'frequency_penalty' => 'decimal:2',
        'presence_penalty' => 'decimal:2',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'api_key'
    ];

    /**
     * 获取配置所属的平台
     */
    public function platform()
    {
        return $this->belongsTo(AiPlatforms::class, 'platform_id', 'platform_id');
    }

    /**
     * 获取配置所属的模型
     */
    public function model()
    {
        return $this->belongsTo(AiModels::class, 'model_id', 'model_id');
    }

    /**
     * 获取默认配置
     */
    public static function getDefaultConfig()
    {
        return self::where('is_default', 1)
                   ->where('is_active', 1)
                   ->with(['platform', 'model'])
                   ->first();
    }

    /**
     * 获取启用的配置列表
     */
    public static function getActiveConfigs()
    {
        return self::where('is_active', 1)
                   ->with(['platform', 'model'])
                   ->get();
    }

    /**
     * 根据配置名称获取配置
     */
    public static function getByName($configName)
    {
        return self::where('config_name', $configName)
                   ->where('is_active', 1)
                   ->with(['platform', 'model'])
                   ->first();
    }

    /**
     * 设置默认配置（确保只有一个默认配置）
     */
    public function setAsDefault()
    {
        // 先取消所有默认配置
        self::where('is_default', 1)->update(['is_default' => 0]);
        
        // 设置当前配置为默认
        $this->is_default = 1;
        $this->save();
        
        return $this;
    }

    /**
     * 获取完整的AI配置信息（包含平台和模型信息）
     */
    public function getFullConfig()
    {
        $this->load(['platform', 'model']);
        
        return [
            'config_id' => $this->config_id,
            'config_name' => $this->config_name,
            'api_key' => $this->api_key,
            'api_endpoint' => $this->api_endpoint ?: $this->platform->api_base_url . '/chat/completions',
            'model_code' => $this->model->model_code,
            'model_name' => $this->model->model_name,
            'platform_name' => $this->platform->platform_name,
            'platform_code' => $this->platform->platform_code,
            'temperature' => $this->temperature,
            'max_tokens' => $this->max_tokens ?: $this->model->max_tokens,
            'frequency_penalty' => $this->frequency_penalty,
            'presence_penalty' => $this->presence_penalty,
            'supports_streaming' => $this->model->supports_streaming,
        ];
    }
}
