<?php

namespace app\model;

use support\Model;

/**
 * ai_platforms AI平台表
 * @property integer $platform_id 平台ID(主键)
 * @property string $platform_name 平台名称
 * @property string $platform_code 平台代码
 * @property string $api_base_url API基础URL
 * @property string $description 平台描述
 * @property integer $is_active 是否启用
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class AiPlatforms extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ai_platforms';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'platform_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'platform_name',
        'platform_code', 
        'api_base_url',
        'description',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取平台下的所有模型
     */
    public function models()
    {
        return $this->hasMany(AiModels::class, 'platform_id', 'platform_id');
    }

    /**
     * 获取平台下的所有配置
     */
    public function configurations()
    {
        return $this->hasMany(AiConfigurations::class, 'platform_id', 'platform_id');
    }

    /**
     * 获取启用的平台
     */
    public static function getActivePlatforms()
    {
        return self::where('is_active', 1)->get();
    }

    /**
     * 根据平台代码获取平台信息
     */
    public static function getByCode($code)
    {
        return self::where('platform_code', $code)->where('is_active', 1)->first();
    }
}
