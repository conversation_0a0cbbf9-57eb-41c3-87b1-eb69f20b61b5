<?php

namespace app\model;

use support\Model;

/**
 * ai_models AI模型表
 * @property integer $model_id 模型ID(主键)
 * @property integer $platform_id 所属平台ID
 * @property string $model_name 模型名称
 * @property string $model_code 模型代码
 * @property string $model_version 模型版本
 * @property integer $max_tokens 最大token数
 * @property integer $supports_streaming 是否支持流式输出
 * @property float $cost_per_1k_tokens 每1K token成本
 * @property string $description 模型描述
 * @property integer $is_active 是否启用
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 */
class AiModels extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'ai_models';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'model_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'platform_id',
        'model_name',
        'model_code',
        'model_version',
        'max_tokens',
        'supports_streaming',
        'cost_per_1k_tokens',
        'description',
        'is_active'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'platform_id' => 'integer',
        'max_tokens' => 'integer',
        'supports_streaming' => 'boolean',
        'cost_per_1k_tokens' => 'decimal:6',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取模型所属的平台
     */
    public function platform()
    {
        return $this->belongsTo(AiPlatforms::class, 'platform_id', 'platform_id');
    }

    /**
     * 获取模型的配置
     */
    public function configurations()
    {
        return $this->hasMany(AiConfigurations::class, 'model_id', 'model_id');
    }

    /**
     * 获取启用的模型
     */
    public static function getActiveModels()
    {
        return self::where('is_active', 1)->with('platform')->get();
    }

    /**
     * 根据平台获取模型
     */
    public static function getByPlatform($platformId)
    {
        return self::where('platform_id', $platformId)
                   ->where('is_active', 1)
                   ->get();
    }

    /**
     * 根据模型代码获取模型信息
     */
    public static function getByCode($modelCode)
    {
        return self::where('model_code', $modelCode)
                   ->where('is_active', 1)
                   ->with('platform')
                   ->first();
    }

    /**
     * 获取支持流式输出的模型
     */
    public static function getStreamingModels()
    {
        return self::where('supports_streaming', 1)
                   ->where('is_active', 1)
                   ->with('platform')
                   ->get();
    }
}
