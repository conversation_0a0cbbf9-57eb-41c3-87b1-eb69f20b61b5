<?php

namespace app\service;

use app\model\AiConfigurations;
use Webman\Openai\Chat;
use InvalidArgumentException;

/**
 * AI服务类，用于处理与OpenAI API的交互
 */
class Ai
{
    private readonly string $apiKey;
    private readonly string $apiEndpoint;
    private readonly string $model;
    private readonly float $temperature;
    private readonly ?int $maxTokens;
    private readonly float $frequencyPenalty;
    private readonly float $presencePenalty;
    private readonly array $fullConfig;

    /**
     * 使用配置初始化AI服务
     *
     * @param string|null $configName 指定配置名称，为空时使用默认配置
     * @throws InvalidArgumentException 如果缺少必需的配置
     */
    public function __construct(?string $configName = null)
    {
        // 从数据库获取AI配置
        $config = $configName ?
            AiConfigurations::getByName($configName) :
            AiConfigurations::getDefaultConfig();

        if (!$config) {
            // 如果数据库中没有配置，回退到.env配置
            $this->initFromEnv();
            return;
        }

        $fullConfig = $config->getFullConfig();
        $this->fullConfig = $fullConfig;

        $this->apiKey = $fullConfig['api_key'];
        $this->apiEndpoint = $fullConfig['api_endpoint'];
        $this->model = $fullConfig['model_code'];
        $this->temperature = $fullConfig['temperature'];
        $this->maxTokens = $fullConfig['max_tokens'];
        $this->frequencyPenalty = $fullConfig['frequency_penalty'];
        $this->presencePenalty = $fullConfig['presence_penalty'];

        if (empty($this->apiKey) || empty($this->apiEndpoint) || empty($this->model)) {
            throw new InvalidArgumentException('缺少必需的AI配置');
        }
    }

    /**
     * 从环境变量初始化配置（回退方案）
     */
    private function initFromEnv(): void
    {
        $this->apiKey = config('ait.ai.API_KEY');
        $this->apiEndpoint = config('ait.ai.API');
        $this->model = config('ait.ai.MODEL');
        $this->temperature = 1.0;
        $this->maxTokens = null;
        $this->frequencyPenalty = 0.0;
        $this->presencePenalty = 0.0;
        $this->fullConfig = [];

        if (empty($this->apiKey) || empty($this->apiEndpoint) || empty($this->model)) {
            throw new InvalidArgumentException('缺少必需的AI配置');
        }
    }

    /**
     * 向AI服务发起补全请求
     *
     * @param array $data 请求数据，包括消息和其他参数
     * @param callable $completeCallback 用于处理完成响应的回调函数
     * @param callable|null $streamCallback 可选，用于处理流式响应的回调函数
     * @throws \Exception 如果API请求失败
     */
    public function completions(array $data, callable $completeCallback, ?callable $streamCallback = null): void
    {
        try {
            $chat = new Chat([
                'apikey' => $this->apiKey,
                'api' => $this->apiEndpoint
            ]);

            // 确保使用配置中的模型和参数
            $data['model'] = $this->model;
            $data['temperature'] = $data['temperature'] ?? $this->temperature;

            // 设置最大token数
            if ($this->maxTokens && !isset($data['max_tokens'])) {
                $data['max_tokens'] = $this->maxTokens;
            }

            // 设置频率惩罚和存在惩罚
            if ($this->frequencyPenalty > 0 && !isset($data['frequency_penalty'])) {
                $data['frequency_penalty'] = $this->frequencyPenalty;
            }
            if ($this->presencePenalty > 0 && !isset($data['presence_penalty'])) {
                $data['presence_penalty'] = $this->presencePenalty;
            }

            $callbacks = [
                'complete' => $completeCallback,
            ];

            if ($streamCallback) {
                $callbacks['stream'] = $streamCallback;
            }

            $chat->completions($data, $callbacks);
        } catch (\Exception $e) {
            // 记录错误或进行适当处理
            throw new \Exception('AI补全请求失败: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 获取当前配置信息
     */
    public function getConfig(): array
    {
        return [
            'api_endpoint' => $this->apiEndpoint,
            'model' => $this->model,
            'temperature' => $this->temperature,
            'max_tokens' => $this->maxTokens,
            'frequency_penalty' => $this->frequencyPenalty,
            'presence_penalty' => $this->presencePenalty,
            'full_config' => $this->fullConfig
        ];
    }

    /**
     * 获取可用的AI配置列表
     */
    public static function getAvailableConfigs(): array
    {
        return AiConfigurations::getActiveConfigs()->toArray();
    }

    /**
     * 检查AI服务是否可用
     */
    public static function isAvailable(): bool
    {
        try {
            $config = AiConfigurations::getDefaultConfig();
            return $config !== null;
        } catch (\Exception $e) {
            // 如果数据库查询失败，检查环境变量
            return !empty(config('ait.ai.API_KEY')) && !empty(config('ait.ai.API'));
        }
    }
}