<?php

namespace app\controller;

use app\model\AiPlatforms;
use app\model\AiModels;
use app\model\AiConfigurations;
use app\service\Ai;
use support\Request;
use support\Response;

/**
 * AI配置管理控制器
 */
class AiConfigController
{
    protected $noNeedLogin = [
        'index','enhanced','admin','switcher','platforms','models','configurations','getConfig','createConfig','updateConfig','deleteConfig','setDefault','testConfig','advancedTestConfig','copyConfig','batchOperation',
        'createPlatform','updatePlatform','deletePlatform','getPlatform',
        'createModel','updateModel','deleteModel','getModel','getAllModels',
        'getActiveConfigurations','getCurrentConfig','switchConfig','getConfigStats','getActiveConfigsForSelector'
    ];
    public function index()
    {
        return view('ai-config/index');
    }

    /**
     * 增强版AI配置管理界面
     */
    public function enhanced()
    {
        return view('ai-config/enhanced');
    }

    /**
     * 管理员界面
     */
    public function admin()
    {
        return view('ai-config/admin');
    }

    /**
     * 模型切换器界面
     */
    public function switcher()
    {
        return view('ai-config/switcher');
    }
    /**
     * 获取所有AI平台列表
     */
    public function platforms(Request $request): Response
    {
        try {
            $platforms = AiPlatforms::getActivePlatforms();
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $platforms
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取平台列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取指定平台的模型列表
     */
    public function models(Request $request): Response
    {
        try {
            $platformId = $request->get('platform_id');
            if (!$platformId) {
                return json([
                    'code' => 400,
                    'message' => '缺少平台ID参数'
                ], 400);
            }

            $models = AiModels::getByPlatform($platformId);
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $models
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取模型列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取所有AI配置列表
     */
    public function configurations(Request $request): Response
    {
        try {
            $configs = AiConfigurations::getActiveConfigs();
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $configs
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 创建新的AI配置
     */
    public function createConfig(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证必需字段
            $required = ['config_name', 'platform_id', 'model_id', 'api_key'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "缺少必需字段: {$field}"
                    ], 400);
                }
            }

            // 检查配置名称是否已存在
            $existing = AiConfigurations::where('config_name', $data['config_name'])->first();
            if ($existing) {
                return json([
                    'code' => 400,
                    'message' => '配置名称已存在'
                ], 400);
            }

            // 创建配置
            $config = AiConfigurations::create([
                'config_name' => $data['config_name'],
                'platform_id' => $data['platform_id'],
                'model_id' => $data['model_id'],
                'api_key' => $data['api_key'],
                'api_endpoint' => $data['api_endpoint'] ?? null,
                'temperature' => $data['temperature'] ?? 0.8,
                'max_tokens' => $data['max_tokens'] ?? null,
                'frequency_penalty' => $data['frequency_penalty'] ?? 0.0,
                'presence_penalty' => $data['presence_penalty'] ?? 0.0,
                'is_default' => $data['is_default'] ?? 0,
                'is_active' => 1
            ]);

            // 如果设置为默认配置，更新其他配置
            if ($data['is_default'] ?? false) {
                $config->setAsDefault();
            }

            return json([
                'code' => 200,
                'message' => '配置创建成功',
                'data' => $config->load(['platform', 'model'])
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新AI配置
     */
    public function updateConfig(Request $request): Response
    {
        try {
            $configId = $request->get('config_id');
            $data = $request->post();

            if (!$configId) {
                return json([
                    'code' => 400,
                    'message' => '缺少配置ID'
                ], 400);
            }

            $config = AiConfigurations::find($configId);
            if (!$config) {
                return json([
                    'code' => 404,
                    'message' => '配置不存在'
                ], 404);
            }

            // 验证必需字段
            $required = ['config_name', 'platform_id', 'model_id', 'api_key'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "缺少必需字段: {$field}"
                    ], 400);
                }
            }

            // 检查配置名称是否重复（排除当前配置）
            $existingConfig = AiConfigurations::where('config_name', $data['config_name'])
                                            ->where('config_id', '!=', $configId)
                                            ->first();
            if ($existingConfig) {
                return json([
                    'code' => 400,
                    'message' => '配置名称已存在'
                ], 400);
            }

            // 更新配置
            $config->update([
                'config_name' => $data['config_name'],
                'platform_id' => $data['platform_id'],
                'model_id' => $data['model_id'],
                'api_key' => $data['api_key'],
                'api_endpoint' => $data['api_endpoint'] ?? null,
                'temperature' => $data['temperature'] ?? 0.8,
                'max_tokens' => $data['max_tokens'] ?? null,
                'frequency_penalty' => $data['frequency_penalty'] ?? 0.0,
                'presence_penalty' => $data['presence_penalty'] ?? 0.0
            ]);

            // 如果设置为默认配置
            if ($data['is_default'] ?? false) {
                $config->setAsDefault();
            }

            return json([
                'code' => 200,
                'message' => '配置更新成功',
                'data' => $config->load(['platform', 'model'])
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除AI配置
     */
    public function deleteConfig(Request $request): Response
    {
        try {
            $configId = $request->get('config_id');

            if (!$configId) {
                return json([
                    'code' => 400,
                    'message' => '缺少配置ID'
                ], 400);
            }

            $config = AiConfigurations::find($configId);
            if (!$config) {
                return json([
                    'code' => 404,
                    'message' => '配置不存在'
                ], 404);
            }

            // 不允许删除默认配置
            if ($config->is_default) {
                return json([
                    'code' => 400,
                    'message' => '不能删除默认配置'
                ], 400);
            }

            $config->delete();

            return json([
                'code' => 200,
                'message' => '配置删除成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 设置默认配置
     */
    public function setDefault(Request $request): Response
    {
        try {
            $configId = $request->post('config_id');

            if (!$configId) {
                return json([
                    'code' => 400,
                    'message' => '缺少配置ID'
                ], 400);
            }

            $config = AiConfigurations::find($configId);
            if (!$config) {
                return json([
                    'code' => 404,
                    'message' => '配置不存在'
                ], 404);
            }

            $config->setAsDefault();

            return json([
                'code' => 200,
                'message' => '默认配置设置成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '设置默认配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 测试AI配置
     */
    public function testConfig(Request $request): Response
    {
        try {
            $configName = $request->post('config_name');
            
            if (!$configName) {
                return json([
                    'code' => 400,
                    'message' => '缺少配置名称'
                ], 400);
            }

            // 使用指定配置创建AI实例
            $ai = new Ai($configName);
            
            // 发送测试请求
            $testData = [
                'messages' => [
                    ['role' => 'user', 'content' => '你好，请回复"测试成功"']
                ]
            ];

            $result = '';
            $ai->completions($testData, function($response) use (&$result) {
                $result = $response['choices'][0]['message']['content'] ?? '无响应';
            });

            return json([
                'code' => 200,
                'message' => '配置测试成功',
                'data' => [
                    'response' => $result,
                    'config' => $ai->getConfig()
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '配置测试失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个配置详情（用于编辑）
     */
    public function getConfig(Request $request): Response
    {
        try {
            $configId = $request->get('config_id');
            if (!$configId) {
                return json([
                    'code' => 400,
                    'message' => '缺少配置ID参数'
                ], 400);
            }

            $config = AiConfigurations::with(['platform', 'model'])->find($configId);
            if (!$config) {
                return json([
                    'code' => 404,
                    'message' => '配置不存在'
                ], 404);
            }

            // 返回配置详情（包含API密钥用于编辑）
            $configData = $config->toArray();
            $configData['api_key'] = $config->api_key; // 显式包含API密钥

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $configData
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 复制配置
     */
    public function copyConfig(Request $request): Response
    {
        try {
            $configId = $request->post('config_id');
            $newName = $request->post('new_name');

            if (!$configId || !$newName) {
                return json([
                    'code' => 400,
                    'message' => '缺少必需参数'
                ], 400);
            }

            // 查找原配置
            $originalConfig = AiConfigurations::find($configId);
            if (!$originalConfig) {
                return json([
                    'code' => 404,
                    'message' => '原配置不存在'
                ], 404);
            }

            // 检查新名称是否重复
            $existingConfig = AiConfigurations::where('config_name', $newName)->first();
            if ($existingConfig) {
                return json([
                    'code' => 400,
                    'message' => '配置名称已存在'
                ], 400);
            }

            // 创建新配置
            $newConfig = AiConfigurations::create([
                'config_name' => $newName,
                'platform_id' => $originalConfig->platform_id,
                'model_id' => $originalConfig->model_id,
                'api_key' => $originalConfig->api_key,
                'api_endpoint' => $originalConfig->api_endpoint,
                'temperature' => $originalConfig->temperature,
                'max_tokens' => $originalConfig->max_tokens,
                'frequency_penalty' => $originalConfig->frequency_penalty,
                'presence_penalty' => $originalConfig->presence_penalty,
                'is_default' => 0, // 复制的配置不设为默认
                'is_active' => 1
            ]);

            return json([
                'code' => 200,
                'message' => '配置复制成功',
                'data' => $newConfig->load(['platform', 'model'])
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '复制配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 高级测试配置（支持自定义测试内容）
     */
    public function advancedTestConfig(Request $request): Response
    {
        try {
            $configName = $request->post('config_name');
            $testMessage = $request->post('test_message', '你好，请回复"测试成功"');
            $temperature = $request->post('temperature');
            $maxTokens = $request->post('max_tokens');

            if (!$configName) {
                return json([
                    'code' => 400,
                    'message' => '缺少配置名称'
                ], 400);
            }

            // 使用指定配置创建AI实例
            $ai = new Ai($configName);

            // 构建测试数据
            $testData = [
                'messages' => [
                    ['role' => 'user', 'content' => $testMessage]
                ]
            ];

            // 如果指定了临时参数，覆盖默认配置
            if ($temperature !== null) {
                $testData['temperature'] = floatval($temperature);
            }
            if ($maxTokens !== null) {
                $testData['max_tokens'] = intval($maxTokens);
            }

            $result = '';
            $startTime = microtime(true);

            $ai->completions($testData, function($response) use (&$result) {
                $result = $response['choices'][0]['message']['content'] ?? '无响应';
            });

            $endTime = microtime(true);
            $responseTime = round(($endTime - $startTime) * 1000, 2); // 毫秒

            return json([
                'code' => 200,
                'message' => '配置测试成功',
                'data' => [
                    'response' => $result,
                    'response_time' => $responseTime,
                    'test_message' => $testMessage,
                    'config' => $ai->getConfig()
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '配置测试失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量操作配置
     */
    public function batchOperation(Request $request): Response
    {
        try {
            $operation = $request->post('operation'); // 'activate', 'deactivate', 'delete'
            $configIds = $request->post('config_ids', []);

            if (!$operation || empty($configIds)) {
                return json([
                    'code' => 400,
                    'message' => '缺少操作类型或配置ID列表'
                ], 400);
            }

            $configs = AiConfigurations::whereIn('config_id', $configIds)->get();
            $successCount = 0;
            $errors = [];

            foreach ($configs as $config) {
                try {
                    switch ($operation) {
                        case 'activate':
                            $config->update(['is_active' => 1]);
                            $successCount++;
                            break;
                        case 'deactivate':
                            if (!$config->is_default) {
                                $config->update(['is_active' => 0]);
                                $successCount++;
                            } else {
                                $errors[] = "配置 '{$config->config_name}' 是默认配置，不能停用";
                            }
                            break;
                        case 'delete':
                            if (!$config->is_default) {
                                $config->delete();
                                $successCount++;
                            } else {
                                $errors[] = "配置 '{$config->config_name}' 是默认配置，不能删除";
                            }
                            break;
                        default:
                            $errors[] = "不支持的操作类型: {$operation}";
                    }
                } catch (\Exception $e) {
                    $errors[] = "处理配置 '{$config->config_name}' 时出错: " . $e->getMessage();
                }
            }

            return json([
                'code' => 200,
                'message' => "批量操作完成，成功处理 {$successCount} 个配置",
                'data' => [
                    'success_count' => $successCount,
                    'errors' => $errors
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '批量操作失败: ' . $e->getMessage()
            ], 500);
        }
    }

    // ==================== 平台管理 ====================

    /**
     * 创建AI平台
     */
    public function createPlatform(Request $request): Response
    {
        try {
            $data = $request->all();

            // 验证必需字段
            $required = ['platform_name', 'platform_code', 'api_base_url'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "缺少必需字段: {$field}"
                    ], 400);
                }
            }

            // 检查平台代码是否重复
            $existingPlatform = AiPlatforms::where('platform_code', $data['platform_code'])->first();
            if ($existingPlatform) {
                return json([
                    'code' => 400,
                    'message' => '平台代码已存在'
                ], 400);
            }

            // 创建平台
            $platform = AiPlatforms::create([
                'platform_name' => $data['platform_name'],
                'platform_code' => $data['platform_code'],
                'api_base_url' => $data['api_base_url'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? 1
            ]);

            return json([
                'code' => 200,
                'message' => '平台创建成功',
                'data' => $platform
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建平台失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新AI平台
     */
    public function updatePlatform(Request $request): Response
    {
        try {
            $platformId = $request->get('platform_id');
            $data = $request->post();

            if (!$platformId) {
                return json([
                    'code' => 400,
                    'message' => '缺少平台ID'
                ], 400);
            }

            $platform = AiPlatforms::find($platformId);
            if (!$platform) {
                return json([
                    'code' => 404,
                    'message' => '平台不存在'
                ], 404);
            }

            // 验证必需字段
            $required = ['platform_name', 'platform_code', 'api_base_url'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "缺少必需字段: {$field}"
                    ], 400);
                }
            }

            // 检查平台代码是否重复（排除当前平台）
            $existingPlatform = AiPlatforms::where('platform_code', $data['platform_code'])
                                          ->where('platform_id', '!=', $platformId)
                                          ->first();
            if ($existingPlatform) {
                return json([
                    'code' => 400,
                    'message' => '平台代码已存在'
                ], 400);
            }

            // 更新平台
            $platform->update([
                'platform_name' => $data['platform_name'],
                'platform_code' => $data['platform_code'],
                'api_base_url' => $data['api_base_url'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? 1
            ]);

            return json([
                'code' => 200,
                'message' => '平台更新成功',
                'data' => $platform
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新平台失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除AI平台
     */
    public function deletePlatform(Request $request): Response
    {
        try {
            $platformId = $request->get('platform_id');

            if (!$platformId) {
                return json([
                    'code' => 400,
                    'message' => '缺少平台ID'
                ], 400);
            }

            $platform = AiPlatforms::find($platformId);
            if (!$platform) {
                return json([
                    'code' => 404,
                    'message' => '平台不存在'
                ], 404);
            }

            // 检查是否有关联的配置
            $configCount = AiConfigurations::where('platform_id', $platformId)->count();
            if ($configCount > 0) {
                return json([
                    'code' => 400,
                    'message' => "无法删除平台，还有 {$configCount} 个配置正在使用此平台"
                ], 400);
            }

            // 检查是否有关联的模型
            $modelCount = AiModels::where('platform_id', $platformId)->count();
            if ($modelCount > 0) {
                return json([
                    'code' => 400,
                    'message' => "无法删除平台，还有 {$modelCount} 个模型属于此平台"
                ], 400);
            }

            $platform->delete();

            return json([
                'code' => 200,
                'message' => '平台删除成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除平台失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个平台详情
     */
    public function getPlatform(Request $request): Response
    {
        try {
            $platformId = $request->get('platform_id');
            if (!$platformId) {
                return json([
                    'code' => 400,
                    'message' => '缺少平台ID参数'
                ], 400);
            }

            $platform = AiPlatforms::with(['models'])->find($platformId);
            if (!$platform) {
                return json([
                    'code' => 404,
                    'message' => '平台不存在'
                ], 404);
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $platform
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取平台详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    // ==================== 模型管理 ====================

    /**
     * 创建AI模型
     */
    public function createModel(Request $request): Response
    {
        try {
            $data = $request->all();

            // 验证必需字段
            $required = ['platform_id', 'model_name', 'model_code'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "缺少必需字段: {$field}"
                    ], 400);
                }
            }

            // 检查平台是否存在
            $platform = AiPlatforms::find($data['platform_id']);
            if (!$platform) {
                return json([
                    'code' => 400,
                    'message' => '指定的平台不存在'
                ], 400);
            }

            // 检查模型代码在该平台下是否重复
            $existingModel = AiModels::where('platform_id', $data['platform_id'])
                                    ->where('model_code', $data['model_code'])
                                    ->first();
            if ($existingModel) {
                return json([
                    'code' => 400,
                    'message' => '该平台下模型代码已存在'
                ], 400);
            }

            // 创建模型
            $model = AiModels::create([
                'platform_id' => $data['platform_id'],
                'model_name' => $data['model_name'],
                'model_code' => $data['model_code'],
                'model_version' => $data['model_version'] ?? null,
                'max_tokens' => $data['max_tokens'] ?? null,
                'supports_streaming' => $data['supports_streaming'] ?? 1,
                'cost_per_1k_tokens' => $data['cost_per_1k_tokens'] ?? null,
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? 1
            ]);

            return json([
                'code' => 200,
                'message' => '模型创建成功',
                'data' => $model->load('platform')
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建模型失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新AI模型
     */
    public function updateModel(Request $request): Response
    {
        try {
            $modelId = $request->get('model_id');
            $data = $request->post();

            if (!$modelId) {
                return json([
                    'code' => 400,
                    'message' => '缺少模型ID'
                ], 400);
            }

            $model = AiModels::find($modelId);
            if (!$model) {
                return json([
                    'code' => 404,
                    'message' => '模型不存在'
                ], 404);
            }

            // 验证必需字段
            $required = ['platform_id', 'model_name', 'model_code'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "缺少必需字段: {$field}"
                    ], 400);
                }
            }

            // 检查平台是否存在
            $platform = AiPlatforms::find($data['platform_id']);
            if (!$platform) {
                return json([
                    'code' => 400,
                    'message' => '指定的平台不存在'
                ], 400);
            }

            // 检查模型代码在该平台下是否重复（排除当前模型）
            $existingModel = AiModels::where('platform_id', $data['platform_id'])
                                    ->where('model_code', $data['model_code'])
                                    ->where('model_id', '!=', $modelId)
                                    ->first();
            if ($existingModel) {
                return json([
                    'code' => 400,
                    'message' => '该平台下模型代码已存在'
                ], 400);
            }

            // 更新模型
            $model->update([
                'platform_id' => $data['platform_id'],
                'model_name' => $data['model_name'],
                'model_code' => $data['model_code'],
                'model_version' => $data['model_version'] ?? null,
                'max_tokens' => $data['max_tokens'] ?? null,
                'supports_streaming' => $data['supports_streaming'] ?? 1,
                'cost_per_1k_tokens' => $data['cost_per_1k_tokens'] ?? null,
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? 1
            ]);

            return json([
                'code' => 200,
                'message' => '模型更新成功',
                'data' => $model->load('platform')
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新模型失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除AI模型
     */
    public function deleteModel(Request $request): Response
    {
        try {
            $modelId = $request->get('model_id');

            if (!$modelId) {
                return json([
                    'code' => 400,
                    'message' => '缺少模型ID'
                ], 400);
            }

            $model = AiModels::find($modelId);
            if (!$model) {
                return json([
                    'code' => 404,
                    'message' => '模型不存在'
                ], 404);
            }

            // 检查是否有关联的配置
            $configCount = AiConfigurations::where('model_id', $modelId)->count();
            if ($configCount > 0) {
                return json([
                    'code' => 400,
                    'message' => "无法删除模型，还有 {$configCount} 个配置正在使用此模型"
                ], 400);
            }

            $model->delete();

            return json([
                'code' => 200,
                'message' => '模型删除成功'
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除模型失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个模型详情
     */
    public function getModel(Request $request): Response
    {
        try {
            $modelId = $request->get('model_id');
            if (!$modelId) {
                return json([
                    'code' => 400,
                    'message' => '缺少模型ID参数'
                ], 400);
            }

            $model = AiModels::with(['platform'])->find($modelId);
            if (!$model) {
                return json([
                    'code' => 404,
                    'message' => '模型不存在'
                ], 404);
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $model
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取模型详情失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取所有模型列表（带平台信息）
     */
    public function getAllModels(Request $request): Response
    {
        try {
            $models = AiModels::with(['platform'])->where('is_active', 1)->get();
            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $models
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取模型列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    // ==================== 模型切换优化功能 ====================

    /**
     * 获取启用的AI配置列表（用于模型切换）
     */
    public function getActiveConfigurations(Request $request): Response
    {
        try {
            $configs = AiConfigurations::where('is_active', 1)
                                     ->with(['platform', 'model'])
                                     ->orderBy('is_default', 'desc')
                                     ->orderBy('config_name', 'asc')
                                     ->get();

            // 格式化配置数据，便于前端使用
            $formattedConfigs = $configs->map(function ($config) {
                return [
                    'config_id' => $config->config_id,
                    'config_name' => $config->config_name,
                    'platform_name' => $config->platform->platform_name,
                    'platform_code' => $config->platform->platform_code,
                    'model_name' => $config->model->model_name,
                    'model_code' => $config->model->model_code,
                    'is_default' => $config->is_default,
                    'temperature' => $config->temperature,
                    'max_tokens' => $config->max_tokens,
                    'display_name' => "{$config->config_name} ({$config->platform->platform_name} - {$config->model->model_name})",
                    'created_at' => $config->created_at,
                    'updated_at' => $config->updated_at
                ];
            });

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $formattedConfigs
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取启用配置列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取当前默认配置信息
     */
    public function getCurrentConfig(Request $request): Response
    {
        try {
            $config = AiConfigurations::getDefaultConfig();

            if (!$config) {
                return json([
                    'code' => 404,
                    'message' => '未找到默认配置'
                ], 404);
            }

            $configData = [
                'config_id' => $config->config_id,
                'config_name' => $config->config_name,
                'platform_name' => $config->platform->platform_name,
                'platform_code' => $config->platform->platform_code,
                'model_name' => $config->model->model_name,
                'model_code' => $config->model->model_code,
                'temperature' => $config->temperature,
                'max_tokens' => $config->max_tokens,
                'api_endpoint' => $config->api_endpoint ?: $config->platform->api_base_url,
                'display_name' => "{$config->config_name} ({$config->platform->platform_name} - {$config->model->model_name})",
                'full_config' => $config->getFullConfig()
            ];

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $configData
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取当前配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 快速切换默认配置
     */
    public function switchConfig(Request $request): Response
    {
        try {
            $configId = $request->post('config_id');

            if (!$configId) {
                return json([
                    'code' => 400,
                    'message' => '缺少配置ID'
                ], 400);
            }

            $config = AiConfigurations::where('config_id', $configId)
                                    ->where('is_active', 1)
                                    ->with(['platform', 'model'])
                                    ->first();
            if (!$config) {
                return json([
                    'code' => 404,
                    'message' => '配置不存在或已停用'
                ], 404);
            }

            // 切换为默认配置
            $config->setAsDefault();

            // 返回新的默认配置信息
            $config->load(['platform', 'model']);
            $configData = [
                'config_id' => $config->config_id,
                'config_name' => $config->config_name,
                'platform_name' => $config->platform->platform_name,
                'model_name' => $config->model->model_name,
                'display_name' => "{$config->config_name} ({$config->platform->platform_name} - {$config->model->model_name})"
            ];

            return json([
                'code' => 200,
                'message' => '配置切换成功',
                'data' => $configData
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '切换配置失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取启用的AI配置列表（用于前端模型选择器）
     */
    public function getActiveConfigsForSelector(Request $request): Response
    {
        try {
            $configs = AiConfigurations::where('is_active', 1)
                                     ->with(['platform', 'model'])
                                     ->orderBy('is_default', 'desc')
                                     ->orderBy('config_name', 'asc')
                                     ->get();

            $formattedConfigs = $configs->map(function ($config) {
                return [
                    'config_id' => $config->config_id,
                    'config_name' => $config->config_name,
                    'model_name' => $config->model->model_name ?? 'Unknown Model',
                    'model_code' => $config->model->model_code ?? 'unknown',
                    'platform_name' => $config->platform->platform_name ?? 'Unknown Platform',
                    'is_default' => $config->is_default,
                    'display_name' => $config->config_name . ' (' . ($config->model->model_name ?? 'Unknown') . ')'
                ];
            });

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $formattedConfigs
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取配置统计信息
     */
    public function getConfigStats(Request $request): Response
    {
        try {
            $totalConfigs = AiConfigurations::count();
            $activeConfigs = AiConfigurations::where('is_active', 1)->count();
            $inactiveConfigs = AiConfigurations::where('is_active', 0)->count();
            $defaultConfig = AiConfigurations::where('is_default', 1)->first();

            // 按平台统计
            $platformStats = AiConfigurations::where('ai_configurations.is_active', 1)
                                           ->join('ai_platforms', 'ai_configurations.platform_id', '=', 'ai_platforms.platform_id')
                                           ->selectRaw('ai_platforms.platform_name, COUNT(*) as count')
                                           ->groupBy('ai_platforms.platform_id', 'ai_platforms.platform_name')
                                           ->get();

            // 按模型统计
            $modelStats = AiConfigurations::where('ai_configurations.is_active', 1)
                                        ->join('ai_models', 'ai_configurations.model_id', '=', 'ai_models.model_id')
                                        ->selectRaw('ai_models.model_name, COUNT(*) as count')
                                        ->groupBy('ai_models.model_id', 'ai_models.model_name')
                                        ->get();

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'total_configs' => $totalConfigs,
                    'active_configs' => $activeConfigs,
                    'inactive_configs' => $inactiveConfigs,
                    'default_config' => $defaultConfig ? [
                        'config_name' => $defaultConfig->config_name,
                        'config_id' => $defaultConfig->config_id
                    ] : null,
                    'platform_stats' => $platformStats,
                    'model_stats' => $modelStats
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计信息失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
